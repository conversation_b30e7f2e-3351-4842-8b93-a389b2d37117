# DeployPackageBuild Operator Documentation

## Overview

The `DeployPackageBuild` operator is an Airflow operator that automates the deployment of metadata package files from S3 to a Git-based metadatastore repository. It downloads metadata package files from an S3 bucket's fullshard/shard directory and uploads them to a metadatastore Git repository on custom draft branches for deployment.

**Primary Purpose:**
- Download metadata package files from S3 storage
- Validate file integrity and dataset consistency
- Create deployment branches in the metadatastore Git repository
- Upload and organize files according to versioned directory structure
- Generate deployment-specific configurations for different environments

## File Structure

### Input File Structure (S3)
The operator expects the following file structure in the S3 `full_shard_url/shard/` directory:

```
s3://{bucket}/{path}/shard/
├── db.json                    # Database schema definition
├── ds.json                    # Dataset specification
├── patientprofile.json        # Patient profile configuration
├── aetionpkg.cs.json         # Coding system package
├── aetionpkg.schema.json     # Schema package
└── aetionpkg.shard.json      # Shard package configuration
```

### Output File Structure (Git Repository)
Files are organized in the metadatastore repository using this structure:

```
metadatastore/
├── metaDataStore/versioned/{name}/{tag}/{revision}/
│   ├── db.json
│   ├── ds.json
│   ├── patientprofile.json
│   └── shard_metadata.json
├── metaDataStore/versioned/package/{name}/{tag}/{revision}/
│   ├── aetionpkg.cs.json
│   └── aetionpkg.schema.json
├── shardpackage/{client-env}/{instance_name_override}/
│   └── aetionpkg.shard.json
└── reportTemplates/{name}/
    └── {name}_datasetDescription.ftl
```

### Key Path Variables
- `versioned_path`: `metaDataStore/versioned/{name}/{tag}/{revision}`
- `versioned_package_path`: `metaDataStore/versioned/package/{name}/{tag}/{revision}`
- `shard_package_path`: `shardpackage/{client-env}/{instance_name_override}`

## Parameters

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `repo` | str | Connector Git repository URL |
| `git_meta_repo` | str | Metadatastore Git repository URL |
| `private_key` | str | SSH private key for Git authentication |
| `git_default_branch` | str | Default Git branch (usually 'master') |
| `transform_path` | str | Path to transformation code in connector repo |
| `data_transformation_url_s3` | str | S3 URL containing transformation files |
| `dataset_artifacts_path` | str | S3 path to dataset artifacts |
| `full_shard_url` | str | S3 URL to the shard directory containing metadata files |
| `gdr_sql` | str | Path to GDR SQL file |
| `client` | str | Client identifier |
| `deployment_config` | Dict[str, Any] | Configuration mapping clients to deployment buckets |
| `deployment_params` | DeploymentParameters | Deployment parameters (deploy/activate flags) |

### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `transform_path_override` | str | None | Override path for transformation code |
| `gdr_validation` | str | None | Path to GDR validation JSON file |
| `resources` | str | "resources" | Resource directory name |
| `fs_factory` | callable | fs_factory_default | File system factory function |
| `branch` | str | None | Git branch override |

### Deployment Configuration Structure

The `deployment_config` parameter expects a dictionary with the following structure:

```python
{
    "client1": {
        "qa": "bucket-qa",
        "prod": "bucket-prod"
    },
    "client2": "bucket-legacy"  # Legacy format for QA-only deployments
}
```

## Output

### Generated Files

1. **Metadata Files**: Core dataset files copied to versioned paths
2. **Shard Package Files**: Environment-specific shard configurations
3. **Report Templates**: Auto-generated dataset description templates
4. **Shard Metadata**: JSON file containing shard deployment metadata

### Git Branches Created

The operator creates separate Git branches for different deployment targets:
- `dbc_{client}_{name}_{tag}_{revision}_legacy_{client}` - For QA/legacy environments
- `dbc_{client}_{name}_{tag}_{revision}_k8` - For Kubernetes environments

### Success Indicators

- Git branches successfully created and pushed
- All metadata files validated and uploaded
- Report templates generated or validated
- DDS (Data Deployment Service) notifications sent

## Usage Examples

### Basic Usage

```python
from operators.deploy_package_build import DeployPackageBuild
from tools.config import DeploymentParameters

deploy_task = DeployPackageBuild(
    task_id='deploy_package_build',
    repo='**************:company/connector.git',
    git_meta_repo='**************:company/metadatastore.git',
    private_key='{{ var.value.git_private_key }}',
    git_default_branch='master',
    transform_path='transformations/dataset1',
    transform_path_override=None,
    data_transformation_url_s3='s3://bucket/etl/dataset1/20240101/',
    dataset_artifacts_path='s3://bucket/artifacts/dataset1/20240101/',
    full_shard_url='s3://bucket/etl/dataset1/20240101/fullshard',
    gdr_sql='s3://bucket/etl/dataset1/20240101/gdr.sql',
    gdr_validation=None,
    client='client1',
    deployment_config={
        'client1': {
            'qa': 'deployment-bucket-qa',
            'prod': 'deployment-bucket-prod'
        }
    },
    deployment_params=DeploymentParameters(deploy=True, activate=False)
)
```

### Advanced Usage with Multiple Clients

```python
deploy_task = DeployPackageBuild(
    task_id='deploy_multi_client',
    repo='**************:company/connector.git',
    git_meta_repo='**************:company/metadatastore.git',
    private_key='{{ var.value.git_private_key }}',
    git_default_branch='master',
    transform_path='transformations/dataset1',
    transform_path_override='transformations/dataset1_override',
    data_transformation_url_s3='s3://bucket/etl/dataset1/20240101/',
    dataset_artifacts_path='s3://bucket/artifacts/dataset1/20240101/',
    full_shard_url='s3://bucket/etl/dataset1/20240101/fullshard',
    gdr_sql='s3://bucket/etl/dataset1/20240101/gdr.sql',
    gdr_validation='s3://bucket/etl/dataset1/20240101/gdr_validation.json',
    client='client1',
    deployment_config={
        'client1': {
            'qa': 'client1-qa-bucket',
            'prod': 'client1-prod-bucket'
        },
        'client2': {
            'qa': 'client2-qa-bucket',
            'prod': 'client2-prod-bucket'
        },
        'legacy_client': 'legacy-bucket'  # QA-only legacy format
    },
    deployment_params=DeploymentParameters(deploy=True, activate=True)
)
```

### DAG Integration Example

```python
from airflow import DAG
from datetime import datetime, timedelta

default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5)
}

dag = DAG(
    'dataset_deployment',
    default_args=default_args,
    description='Deploy dataset package to metadatastore',
    schedule_interval=None,
    catchup=False
)

deploy_package = DeployPackageBuild(
    task_id='deploy_package_build',
    dag=dag,
    # ... parameters as shown in examples above
)
```

## Dependencies

### Required Python Packages
- `airflow` - Apache Airflow framework
- `pandas` - For Excel file processing
- `importlib_resources` - For resource file access
- `fsspec` - File system abstraction
- `exceptiongroup` - Exception handling

### Required Custom Modules
- `hooks.git_hook.GitHook` - Git operations
- `tools.deploy_common.DeployCommonHook` - Common deployment logic
- `tools.config.DeploymentParameters` - Configuration management
- `tools.git_credentials.GitCredentials` - Git authentication
- `operators.aws` - AWS/S3 operations

### External Dependencies
- Git repositories (connector and metadatastore)
- S3 storage access
- SSH key authentication for Git
- DDS (Data Deployment Service) for notifications

## Error Handling

The operator includes comprehensive validation and error handling:

### Validation Checks
- **File Existence**: Validates all required metadata files exist in S3
- **Git Synchronization**: Ensures transformation files in Git match S3 versions
- **Dataset Name Consistency**: Validates dataset names don't change between revisions
- **Attribute Prefix Validation**: Checks patient attribute prefixes match dataset names
- **Shard Package Validation**: Validates shard package values against previous revisions

### Exception Types
- `AirflowException` - General operational failures
- `DatasetNameChangeException` - Dataset name inconsistency between revisions
- `InvalidShardPackageValueException` - Invalid shard package configuration values
- `ExceptionGroup` - Multiple validation failures

### Recovery Mechanisms
- Temporary directory cleanup on failure
- Git branch cleanup on errors
- Detailed logging for troubleshooting
- Partial success handling with exception aggregation
