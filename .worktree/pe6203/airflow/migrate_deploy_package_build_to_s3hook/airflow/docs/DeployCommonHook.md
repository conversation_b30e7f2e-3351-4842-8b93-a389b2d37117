# DeployCommonHook Class Documentation

## Overview

The `DeployCommonHook` class provides common deployment logic shared between the `CreateDeploymentPackage` and `DeployPackageBuild` operators. It serves as a centralized utility for managing metadata package files, report templates, and Git repository operations during the deployment process.

**Primary Purpose:**
- Validate and manage report templates in the metadatastore repository
- Update and process Aetion package files (aetionpkg.shard.json)
- Create shard metadata JSON files for deployment tracking
- Provide S3 file system access for deployment operations
- Handle UUID mapping for report templates

## Class Structure

### Constructor

```python
def __init__(self, git_creds: GitCredentials, aws_conn_id: str = "aws_default")
```

### Properties

- `fs` (cached_property): Returns an `AetionS3FileSystem` instance for S3 operations

### Public Methods

1. `validate_report_template_exists()` - Validates report template existence
2. `get_report()` (static) - Finds matching report template from UUID mapping
3. `find_report_template()` - Locates report template file in repository
4. `update_aetion_pkg_file()` - Updates Aetion package files with deployment-specific values
5. `create_shard_metadata_json()` (static) - Creates shard metadata JSON files

## Input Parameters

### Constructor Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `git_creds` | GitCredentials | Yes | - | Git credentials containing repository URLs, private key, and branch information |
| `aws_conn_id` | str | No | "aws_default" | Airflow connection ID for AWS/S3 access |

### GitCredentials Structure

The `git_creds` parameter expects a `GitCredentials` dataclass with:

```python
@dataclass
class GitCredentials:
    private_key: str          # SSH private key for Git authentication
    branch: str              # Git branch name (usually 'master')
    connector_repo: str      # Connector repository URL
    metadatastore_repo: str  # Metadatastore repository URL
```

## External Systems

### Git Repositories
- **Metadatastore Repository**: Contains versioned metadata files, report templates, and UUID mappings
- **Connector Repository**: Contains transformation code and configuration files

### File Systems
- **S3 Storage**: Source for metadata package files and deployment artifacts
- **Local File System**: Temporary storage during Git operations and file processing

### Report Template Management
- **UUID Mapping**: CSV file (`uuidMap.csv`) mapping dataset identifiers to report template UUIDs
- **Report Templates**: FreeMarker template files (`.ftl`) for dataset descriptions

## File Structure

### Input File Structure (S3)
```
s3://{bucket}/{path}/shard/
└── aetionpkg.shard.json    # Shard package configuration file
```

### Output File Structure (Git Repository)
```
metadatastore/
├── metaDataStore/versioned/{name}/{tag}/{revision}/
│   └── shard_metadata.json
├── reportTemplates/
│   ├── uuidMap.csv
│   └── {dataset_name}/
│       └── {dataset_name}_datasetDescription.ftl
└── shardpackage/{client-env}/{instance_name}/
    └── aetionpkg.shard.json
```

### UUID Mapping File Format
The `uuidMap.csv` file contains mappings in the format:
```csv
dataset_name/tag/revision/template_file.ftl,uuid-value
dataset_name/tag/template_file.ftl,uuid-value
dataset_name/template_file.ftl,uuid-value
```

## Methods Documentation

### validate_report_template_exists()

**Purpose**: Validates that a report template exists for the specified dataset

```python
def validate_report_template_exists(
    self, tmp_git: str, dataset_name: str, tag: str, revision: str
) -> None
```

**Parameters:**
- `tmp_git` (str): Path to local Git repository clone
- `dataset_name` (str): Name of the dataset
- `tag` (str): Dataset tag/instance identifier
- `revision` (str): Dataset revision identifier

**Raises:**
- `AirflowException`: If report template is not found

**Usage Example:**
```python
hook.validate_report_template_exists("/tmp/repo", "dataset1", "client1", "20240101")
```

### get_report() (Static Method)

**Purpose**: Finds the most specific matching report template from UUID mapping

```python
@staticmethod
def get_report(
    metadata: Path, name: str, tag: str, revision: str
) -> ReportTemplate | None
```

**Parameters:**
- `metadata` (Path): Path to metadatastore repository
- `name` (str): Dataset name
- `tag` (str): Dataset tag
- `revision` (str): Dataset revision

**Returns:**
- `ReportTemplate | None`: Report template object or None if not found

**Matching Logic:**
1. Tries exact match: `name/tag/revision`
2. Falls back to: `name/tag`
3. Falls back to: `name`

### find_report_template()

**Purpose**: Locates and validates report template file existence

```python
def find_report_template(
    self, metadatastore_repo_dir: Path, name: str, tag: str, revision: str
)
```

**Parameters:**
- `metadatastore_repo_dir` (Path): Local metadatastore repository path
- `name` (str): Dataset name
- `tag` (str): Dataset tag
- `revision` (str): Dataset revision

**Returns:**
- `Path`: Relative path to report template file

**Error Conditions:**
- Raises `AirflowException` if UUID mapping not found
- Raises `AirflowException` if template file doesn't exist

### update_aetion_pkg_file()

**Purpose**: Updates Aetion package file with deployment-specific configurations

```python
def update_aetion_pkg_file(
    self,
    file_s3_path: str,
    file_local_path: str,
    package_file: PackageFileMetadata,
    dataset_name: str,
    tag: str,
    revision: str,
    etl_dir: str,
) -> Dict[str, Any]
```

**Parameters:**
- `file_s3_path` (str): S3 path to source package file
- `file_local_path` (str): Local destination path
- `package_file` (PackageFileMetadata): Package file metadata
- `dataset_name` (str): Dataset name
- `tag` (str): Dataset tag
- `revision` (str): Dataset revision
- `etl_dir` (str): ETL directory path

**Returns:**
- `Dict[str, Any]`: Updated package file content

**Transformations Applied:**
- Updates `runtimeDir` if bucket override specified
- Updates `shardGUID` if UUID override specified
- Always updates `etlDir` with provided ETL directory

### create_shard_metadata_json() (Static Method)

**Purpose**: Creates shard metadata JSON file for deployment tracking

```python
@staticmethod
def create_shard_metadata_json(
    metadatastore_path: Path, shard_package_data=Dict[str, Any]
) -> None
```

**Parameters:**
- `metadatastore_path` (Path): Target directory for metadata file
- `shard_package_data` (Dict[str, Any]): Source shard package data

**Output File:** `shard_metadata.json`

**Extracted Fields:**
- `name`, `tag`, `revision`
- `shardNum`, `patientNum`
- `instanceNameOverride`
- `sampleShardNum`, `sampleShardPct`, `supportSampleData`, `sampleDataPatientNum`
- `dataModel`, `dataModelVersion`
- `etlDir`
- `startDate`, `endDate`, `createDate`
- `eventsDiscardedOutsideGDR`

## Usage Examples

### Basic Instantiation

```python
from tools.deploy_common import DeployCommonHook
from tools.git_credentials import GitCredentials

# Create Git credentials
git_creds = GitCredentials(
    private_key="-----BEGIN OPENSSH PRIVATE KEY-----\n...",
    branch="master",
    connector_repo="**************:company/connector.git",
    metadatastore_repo="**************:company/metadatastore.git"
)

# Create hook instance
hook = DeployCommonHook(git_creds=git_creds, aws_conn_id="aws_default")
```

### Report Template Validation

```python
# Validate report template exists
try:
    hook.validate_report_template_exists(
        tmp_git="/tmp/metadatastore",
        dataset_name="patient_data",
        tag="client1",
        revision="20240101"
    )
    print("Report template found and validated")
except AirflowException as e:
    print(f"Report template validation failed: {e}")
```

### Package File Update

```python
from tools.deploy_common import PackageFileMetadata

# Create package file metadata
package_file = PackageFileMetadata(
    filename="aetionpkg.shard.json",
    metadatastore_path="shardpackage/client1-prod/instance1",
    bucket_override="deployment-bucket-prod",
    shard_uuid_override="550e8400-e29b-41d4-a716-************"
)

# Update package file
updated_content = hook.update_aetion_pkg_file(
    file_s3_path="s3://source-bucket/shard/aetionpkg.shard.json",
    file_local_path="/tmp/updated_aetionpkg.shard.json",
    package_file=package_file,
    dataset_name="patient_data",
    tag="client1",
    revision="20240101",
    etl_dir="s3://etl-bucket/patient_data/client1/20240101"
)
```

### Shard Metadata Creation

```python
from pathlib import Path

# Sample shard package data
shard_data = {
    "name": "patient_data",
    "tag": "client1",
    "revision": "20240101",
    "shardNum": 1,
    "patientNum": 10000,
    "instanceNameOverride": "instance1",
    "sampleShardNum": 1,
    "sampleShardPct": 0.1,
    "supportSampleData": True,
    "dataModel": "OMOP",
    "dataModelVersion": "5.4",
    "etlDir": "s3://etl-bucket/patient_data/client1/20240101/",
    "startDate": "2020-01-01",
    "endDate": "2023-12-31",
    "createDate": "2024-01-01"
}

# Create shard metadata JSON
DeployCommonHook.create_shard_metadata_json(
    metadatastore_path=Path("/tmp/metadatastore/versioned/patient_data/client1/20240101"),
    shard_package_data=shard_data
)
```

### Integration with DeployPackageBuild

```python
from operators.deploy_package_build import DeployPackageBuild

# The hook is automatically created and used within operators
operator = DeployPackageBuild(
    # ... other parameters ...
    git_creds=git_creds,
    aws_conn_id="aws_default"
)

# Hook is accessible as operator.deploy_hook
# and is used internally for:
# - Report template validation
# - Package file updates
# - Shard metadata creation
```

## Error Handling

### Common Exceptions

- **AirflowException**: Raised for operational failures including:
  - Missing report template UUID mappings
  - Report template file not found
  - Duplicate entries in UUID mapping file
  - S3 access failures

### Validation Checks

- **Report Template Validation**: Ensures UUID mapping exists and template file is present
- **File Existence**: Validates S3 source files exist before processing
- **UUID Mapping Integrity**: Checks for duplicate entries in mapping file

### Recovery Mechanisms

- **Graceful Degradation**: Methods return None or raise specific exceptions for missing resources
- **Detailed Logging**: Comprehensive logging for troubleshooting deployment issues
- **File System Cleanup**: Automatic cleanup of temporary files and directories
