"""Deploy Package Build Operator Module.

This module provides the DeployPackageBuild operator for Airflow, which handles the deployment
of metadata package files from S3 to a Git repository. It manages the complete workflow of
downloading package files, validating them, and uploading them to the metadatastore repository.

File Structure:
    The operator works with the following file structure:

    S3 Structure:
        s3://bucket/fullshard/shard/
        ├── db.json                    # Database configuration
        ├── ds.json                    # Dataset configuration
        ├── patientprofile.json        # Patient profile data
        ├── aetionpkg.cs.json         # Common schema package
        ├── aetionpkg.schema.json     # Schema package
        └── aetionpkg.shard.json      # Shard package configuration

    Git Repository Structure:
        metadatastore/
        ├── metaDataStore/versioned/
        │   ├── {dataset_name}/{tag}/{revision}/
        │   │   ├── db.json
        │   │   ├── ds.json
        │   │   └── patientprofile.json
        │   └── package/{dataset_name}/{tag}/{revision}/
        │       ├── aetionpkg.cs.json
        │       └── aetionpkg.schema.json
        ├── shardpackage/{client}-{env}/{instance_name}/
        │   └── aetionpkg.shard.json
        └── reportTemplates/
            ├── uuidMap.csv
            └── {dataset_name}/
                └── {dataset_name}_datasetDescription.ftl

Classes:
    DeployPackageBuild: Main operator for deploying package builds
    DatasetNameChangeException: Exception for dataset name changes
    InvalidShardPackageValueException: Exception for invalid shard package values

Example Usage:
    ```python
    deploy_op = DeployPackageBuild(
        repo="**************:aetion/connector.git",
        git_meta_repo="**************:aetion/metadatastore.git",
        private_key="-----BEGIN OPENSSH PRIVATE KEY-----...",
        git_default_branch="master",
        transform_path="transforms/dataset1",
        transform_path_override=None,
        data_transformation_url_s3="s3://bucket/transforms/",
        dataset_artifacts_path="s3://bucket/artifacts/",
        full_shard_url="s3://bucket/fullshard/",
        gdr_sql="s3://bucket/gdr.sql",
        gdr_validation="s3://bucket/gdr-validation.json",
        client="client1",
        deployment_config={
            "client1": {"qa": "qa-bucket", "prod": "prod-bucket"},
            "client2": "legacy-bucket"
        },
        deployment_params=DeploymentParameters(deploy=True, activate=False),
        task_id="deploy_package_build"
    )
    ```
"""

import csv
import datetime
import json
import logging
import os
import shutil
import tempfile
from functools import cached_property
from pathlib import Path
from typing import Any

import importlib_resources
import pandas as pd
from airflow.exceptions import AirflowException
from airflow.models import BaseOperator
from airflow.utils.context import Context
from dbc_utilities.dbc_validator.constants import (
    METADATA_VERSIONED_PREFIX,
    TRANSFORMATION_REPO_ROOT,
)
from exceptiongroup import ExceptionGroup

from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem
from hooks.git_hook import GitHook
from operators.patient_prep import PatientPrep
from operators.utils import DATA_SPEC, RevisionLocator
from tools.config import DeploymentParameters
from tools.dds import notify_dds
from tools.deploy_common import (
    REPORT_DIR,
    SHARD_METADATA_JSON,
    UUID_MAP_CSV,
    DeployCommonHook,
    DeploymentBranchMetadata,
    PackageFileMetadata,
    ShardDeploymentMetadata,
)
from tools.git_credentials import GitCredentials
from tools.git_utils import validate_and_hash
from tools.shard_util import generate_shard_uuid

logger = logging.getLogger(__name__)


class DatasetNameChangeException(AirflowException):
    """Exception raised when dataset name changes between revisions.

    This exception is raised during validation when the dataset name
    in the current revision differs from the previous revision, which
    could indicate an unintended change that might break deployment.

    Args:
        message: Description of the dataset name change

    Example:
        ```python
        raise DatasetNameChangeException(
            "Dataset name has changed from PATIENT_DATA to CLINICAL_DATA"
        )
        ```
    """

    pass


class InvalidShardPackageValueException(AirflowException):
    """Exception raised when shard package values change inappropriately.

    This exception is raised when critical shard package configuration
    values change between revisions in ways that could cause deployment
    or data consistency issues.

    Args:
        changed_field: Name of the field that changed
        prev_value_path: Path to the previous revision file
        prev_value: Previous value of the field
        cur_value: Current value of the field

    Example:
        ```python
        raise InvalidShardPackageValueException(
            changed_field="sampleShardNum",
            prev_value_path="/path/to/prev/aetionpkg.shard.json",
            prev_value=5,
            cur_value=10
        )
        ```
    """

    def __init__(
        self,
        changed_field: str,
        prev_value_path: str,
        prev_value: Any,
        cur_value: Any,
        *args: Any,
        **kwargs: Any,
    ) -> None:
        """Initialize the exception with field change details."""
        message = (
            f"Shard package field '{changed_field}' changed from {prev_value} "
            f"to {cur_value}. Previous file: {prev_value_path}"
        )
        super().__init__(message, *args, **kwargs)
        self.changed_field = changed_field
        self.prev_value_path = prev_value_path
        self.prev_value = prev_value
        self.cur_value = cur_value


class DeployPackageBuild(BaseOperator):
    """Deploy Package Build Operator.

    Downloads metadata package files from S3 bucket (fullshard/shard directory) and uploads
    them to the metadatastore Git repository in custom draft branches. This operator handles
    the complete deployment workflow including validation, file processing, and Git operations.

    The operator processes the following metadata files:
    - db.json: Database configuration
    - ds.json: Dataset configuration
    - patientprofile.json: Patient profile data
    - aetionpkg.cs.json: Common schema package
    - aetionpkg.schema.json: Schema package
    - aetionpkg.shard.json: Shard package configuration

    Attributes:
        repo: Connector repository URL
        git_meta_repo: Metadatastore repository URL
        private_key: SSH private key for Git authentication
        git_default_branch: Default Git branch (usually 'master')
        transform_path: Path to transformation code
        transform_path_override: Optional override path for transformations
        data_transformation_url_s3: S3 URL for transformation data
        full_shard_url: S3 URL for full shard data
        gdr_sql: S3 path to GDR SQL file
        gdr_validation: S3 path to GDR validation JSON file
        client: Client identifier
        deployment_config: Configuration for deployment environments
        deployment_params: Deployment parameters (deploy/activate flags)

    Example:
        ```python
        deploy_op = DeployPackageBuild(
            repo="**************:aetion/connector.git",
            git_meta_repo="**************:aetion/metadatastore.git",
            private_key="-----BEGIN OPENSSH PRIVATE KEY-----...",
            git_default_branch="master",
            transform_path="transforms/dataset1",
            transform_path_override=None,
            data_transformation_url_s3="s3://bucket/transforms/",
            dataset_artifacts_path="s3://bucket/artifacts/",
            full_shard_url="s3://bucket/fullshard/",
            gdr_sql="s3://bucket/gdr.sql",
            gdr_validation="s3://bucket/gdr-validation.json",
            client="client1",
            deployment_config={
                "client1": {"qa": "qa-bucket", "prod": "prod-bucket"}
            },
            deployment_params=DeploymentParameters(deploy=True, activate=False),
            task_id="deploy_package_build"
        )
        ```
    """

    template_fields = (
        "repo",
        "git_meta_repo",
        "private_key",
        "git_default_branch",
        "transform_path",
        "transform_path_override",
        "data_transformation_url_s3",
        "full_shard_url",
        "gdr_sql",
        "client",
    )
    template_ext = ()

    def __init__(
        self,
        repo: str,
        git_meta_repo: str,
        private_key: str,
        git_default_branch: str,
        transform_path: str,
        transform_path_override: str | None,
        data_transformation_url_s3: str,
        dataset_artifacts_path: str,
        full_shard_url: str,
        gdr_sql: str,
        gdr_validation: str | None,
        client: str,
        deployment_config: dict[str, Any],
        deployment_params: DeploymentParameters,
        resources: str = "resources",
        aws_conn_id: str = "aws_default",
        branch: str | None = None,
        *args: Any,
        **kwargs: Any,
    ) -> None:
        """Initialize the DeployPackageBuild operator.

        Args:
            repo: Connector repository URL (e.g., "**************:aetion/connector.git")
            git_meta_repo: Metadatastore repository URL (e.g., "**************:aetion/metadatastore.git")
            private_key: SSH private key for Git authentication
            git_default_branch: Default Git branch name (e.g., "master")
            transform_path: Path to transformation code (e.g., "transforms/dataset1")
            transform_path_override: Optional override path for transformations
            data_transformation_url_s3: S3 URL for transformation data (e.g., "s3://bucket/transforms/")
            dataset_artifacts_path: S3 path to dataset artifacts (e.g., "s3://bucket/artifacts/")
            full_shard_url: S3 URL for full shard data (e.g., "s3://bucket/fullshard/")
            gdr_sql: S3 path to GDR SQL file (e.g., "s3://bucket/gdr.sql")
            gdr_validation: S3 path to GDR validation JSON file (e.g., "s3://bucket/gdr-validation.json")
            client: Client identifier (e.g., "client1")
            deployment_config: Configuration for deployment environments
                Example: {"client1": {"qa": "qa-bucket", "prod": "prod-bucket"}}
            deployment_params: Deployment parameters with deploy/activate flags
            resources: Resource directory name (default: "resources")
            aws_conn_id: Airflow AWS connection ID (default: "aws_default")
            branch: Optional branch override
            *args: Additional positional arguments for BaseOperator
            **kwargs: Additional keyword arguments for BaseOperator

        Example:
            ```python
            operator = DeployPackageBuild(
                repo="**************:aetion/connector.git",
                git_meta_repo="**************:aetion/metadatastore.git",
                private_key="-----BEGIN OPENSSH PRIVATE KEY-----...",
                git_default_branch="master",
                transform_path="transforms/patient_data",
                transform_path_override=None,
                data_transformation_url_s3="s3://data-bucket/transforms/",
                dataset_artifacts_path="s3://data-bucket/artifacts/",
                full_shard_url="s3://data-bucket/fullshard/",
                gdr_sql="s3://data-bucket/gdr.sql",
                gdr_validation="s3://data-bucket/gdr-validation.json",
                client="healthcare_client",
                deployment_config={
                    "healthcare_client": {
                        "qa": "healthcare-qa-bucket",
                        "prod": "healthcare-prod-bucket"
                    }
                },
                deployment_params=DeploymentParameters(deploy=True, activate=False),
                task_id="deploy_package_build"
            )
            ```
        """
        BaseOperator.__init__(self, *args, **kwargs)
        self.aws_conn_id = aws_conn_id
        self.repo = repo
        self.git_meta_repo = git_meta_repo
        self.private_key = private_key
        self.git_default_branch = git_default_branch
        self.git_creds = GitCredentials(
            connector_repo=self.repo,
            metadatastore_repo=self.git_meta_repo,
            private_key=self.private_key,
            branch=self.git_default_branch,
        )
        self.deploy_hook = DeployCommonHook(
            git_creds=self.git_creds, aws_conn_id=self.aws_conn_id
        )
        self.transform_path = transform_path
        self.transform_path_override = transform_path_override
        self.data_transformation_url_s3 = data_transformation_url_s3
        self.spec_s3_path = (
            f"{str(dataset_artifacts_path).replace('s3a://', 's3://', 1)}{DATA_SPEC}"
        )
        self.full_shard_url = full_shard_url
        self.gdr_sql = gdr_sql
        self.gdr_validation = gdr_validation
        self.client = client
        self.deployment_config = deployment_config
        self.legacy_mismatched_datasets = json.loads(
            importlib_resources.files(resources)
            .joinpath("legacy_mismatched_datasets.json")
            .read_text()
        )
        self.deployment_params = deployment_params

        self.name: str | None = None
        self.revision: str | None = None
        self.tag: str | None = None
        self.instance_name_override: str | None = None
        # can't be set until populate_attributes() is called
        self.versioned_path: str | None = (
            None  # f'metaDataStore/versioned/{self.name}/{self.tag}/{self.revision}'
        )
        self.versioned_package_path: str | None = (
            None  # f'metaDataStore/versioned/package/{self.name}/{self.tag}/{self.revision}'
        )
        self.shard_package_exceptions: list[Exception] = []

    @cached_property
    def fs(self) -> AetionS3FileSystem:
        """Get the S3 filesystem instance.

        Returns:
            AetionS3FileSystem: Configured S3 filesystem for file operations.
        """
        return AetionS3FileSystem(self.aws_conn_id)

    @cached_property
    def revision_locator(self) -> RevisionLocator:
        """Get the revision locator instance.

        Returns:
            RevisionLocator: Utility for finding and comparing revisions.
        """
        return RevisionLocator(self.fs)

    def __validate_attribute_prefix_vs_dataset_name(self) -> bool:
        """Validate that patient attribute prefix matches dataset name.

        Checks that the patient attribute prefix in db.json matches the dataset name,
        with special handling for legacy datasets that may have mismatched prefixes.

        Returns:
            bool: True if validation passes.

        Raises:
            AirflowException: If db.json cannot be read or validation fails.

        Example:
            For dataset "PATIENT_DATA", expects patient attributes like:
            "PATIENT_DATA/demographics/age" where "PATIENT_DATA" is the prefix.
        """
        logger.info("validating patient attribute prefix vs dataset name")
        try:
            with self.fs.open(
                os.path.join(self.full_shard_url, "shard", "db.json"), "r"
            ) as f:
                db_shard = json.loads(f.read())
        except Exception as e:
            raise AirflowException(f"cannot read file: db.json - {e}") from e

        if self.name is None:
            raise AirflowException(
                "Dataset name not populated. Call populate_attributes() first."
            )

        dataset_name = self.name.upper()
        attribute_prefix = db_shard["patientAttributes"][0]["name"].split("/")[0]

        logger.info(f"found patient attribute prefix: {attribute_prefix}")
        logger.info(f"found dataset_name: {dataset_name}")

        legacy_datasets_and_prefixes = self.legacy_mismatched_datasets.get(self.client)
        logger.debug(
            f"mismatched legacy datasets: {json.dumps(legacy_datasets_and_prefixes, indent=2)}"
        )

        if (
            legacy_datasets_and_prefixes is not None
            and legacy_datasets_and_prefixes.get(dataset_name) is not None
        ):
            legacy_prefix = legacy_datasets_and_prefixes.get(dataset_name)

            logger.info(
                f"found mismatched legacy dataset_name: {dataset_name} for client {self.client}"
            )

            if attribute_prefix != legacy_prefix:
                raise AirflowException(
                    "Attribute prefix in db.json does not match legacy attribute prefix. "
                    "Please reach out to support."
                )
        else:
            if dataset_name != attribute_prefix:
                raise AirflowException(
                    f"dataset name {dataset_name} does not match attribute prefix {attribute_prefix}"
                )

        return True

    def __tag_master_branch(
        self, git: GitHook, tag_name: str, tag_message: str
    ) -> None:
        """Tag the master branch with deployment information.

        Args:
            git: Git hook instance for repository operations
            tag_name: Name for the Git tag (e.g., "DBC_CLIENT1_DATASET_TAG_REVISION-2024-01-01-12-00-00-123456")
            tag_message: Message for the Git tag containing deployment details

        Example:
            ```python
            self.__tag_master_branch(
                git_hook,
                "DBC_CLIENT1_PATIENT_DATA_V1_20240101-2024-01-01-12-00-00-123456",
                "DBC_CLIENT1_PATIENT_DATA_V1_20240101 (ADIP)\n\nGDR.SQL: SELECT * FROM patients..."
            )
            ```
        """
        logger.info(f"adding tag: {tag_name}")
        new_tag = git.repo.create_tag(tag_name, message=tag_message)
        git.repo.remotes.origin.push(new_tag)

    def __validate_if_files_are_in_master(self, tmp_git: str) -> None:
        """Validate that transformation files exist in the master branch and match S3.

        Checks that:
        1. Transformation paths exist in the Git repository
        2. No directories are present in transformation folders
        3. File count matches between Git and S3
        4. File hashes match between Git and S3

        Args:
            tmp_git: Path to the temporary Git repository clone

        Raises:
            AirflowException: If validation fails for any reason

        Example:
            For transform_path="transforms/patient_data", validates:
            - Git: {tmp_git}/dbc/transforms/patient_data/
            - S3: s3://bucket/transforms/patient_data/
        """
        transformation_paths = [self.transform_path]
        if self.transform_path_override:
            transformation_paths.append(self.transform_path_override)
        transformation_full_paths = [
            os.path.join(tmp_git, TRANSFORMATION_REPO_ROOT, path)
            for path in transformation_paths
        ]
        # check that transform path in airflow exists in git
        for transform_path in transformation_full_paths:
            if not os.path.exists(transform_path):
                err = (
                    f"Transformation code not found in git {self.git_creds.branch}: {transform_path}, "
                    f"please check in your changes into {self.git_creds.branch} "
                )
                raise AirflowException(err)
            for file in os.listdir(transform_path):
                # check no directories in git
                if os.path.isdir(os.path.join(transform_path, file)):
                    raise AirflowException(
                        "Directories in transform folder are not allowed"
                    )

        transformation_merged_path = transformation_full_paths[0]
        if self.transform_path_override:
            transform_path, transform_path_override = [
                Path(x) for x in transformation_full_paths
            ]
            transformation_merged_path = PatientPrep._merge_transform_code_directories(
                transform_path, transform_path_override
            )

        # check that number of files in s3 matches the number of files in git
        s3_file_list = self.fs.ls(
            self.data_transformation_url_s3.replace("s3a://", "s3://", 1), False, True
        )
        # Filter out files that should not be in git (gdr.sql and enum_lookup.csv)
        excluded_files = ["gdr.sql", "enum_lookup.csv"]
        s3_files_to_count = [
            f
            for f in s3_file_list
            if not any(excluded_file in f for excluded_file in excluded_files)
        ]
        num_files_in_s3 = len(s3_files_to_count)
        git_file_list = os.listdir(transformation_merged_path)
        num_files_in_git = len(git_file_list)
        if num_files_in_git != num_files_in_s3:
            raise AirflowException(
                f"Number of files in git branch {self.git_creds.branch} ({git_file_list}) "
                f"does not match number of files in"
                f"s3 ({s3_file_list}), please check in your changes into {self.git_creds.branch}"
            )

        for file in git_file_list:
            s3_hash, git_hash = validate_and_hash(
                file_name=file,
                transformation_dir=Path(transformation_merged_path),
                fs=self.fs,
                s3_file_path=os.path.join(self.data_transformation_url_s3, file),
                branch=self.git_creds.branch,
            )
            if s3_hash != git_hash:
                err = (
                    f"File in {self.git_creds.branch} is not up to date - file: {file}, "
                    f"please check in your changes into {self.git_creds.branch} "
                )
                raise AirflowException(err)

    def __validate_deployment_config(self) -> None:
        """Validate that deployment configuration is properly set.

        Raises:
            AirflowException: If deployment_config is None or not set.

        Example:
            Valid deployment_config:
            ```python
            {
                "client1": {"qa": "client1-qa-bucket", "prod": "client1-prod-bucket"},
                "client2": "client2-legacy-bucket"
            }
            ```
        """
        if self.deployment_config is None:
            err = "ERROR : The deployment_config airflow variable is not set. "
            logger.warning(err)
            raise AirflowException(err)

    def get_branches(self) -> list[DeploymentBranchMetadata]:
        """Generate deployment branch metadata for different environments.

        Creates separate branches for legacy shard packages and one branch for Kubernetes packages.
        Legacy clients get individual branches, while Kubernetes environments are grouped together.

        Returns:
            list[DeploymentBranchMetadata]: List of branch metadata for deployment

        Example:
            For deployment_config:
            ```python
            {
                "client1": {"qa": "bucket1", "prod": "bucket2"},
                "client2": "legacy-bucket"
            }
            ```

            Returns branches:
            - "legacy_client1" (for QA environment)
            - "legacy_client2" (for legacy environment)
            - "k8" (for prod environment)
        """
        # create separate branch for each legacy shard package and one branch for k8 packages
        branches: list[DeploymentBranchMetadata] = []
        k8_branch: list[ShardDeploymentMetadata] = []
        for client, info in self.deployment_config.items():
            shard_guid = generate_shard_uuid(
                self.name, self.tag, self.revision, client
            )  # dataset should have same guid in legacy and kubernetes envs
            if isinstance(info, dict):
                for env, bucket in info.items():
                    if env == "qa":
                        branches.append(
                            DeploymentBranchMetadata(
                                branch_suffix=f"legacy_{client}",
                                shard_files=[
                                    ShardDeploymentMetadata(
                                        client_environment_name=f"{client}-{env}",
                                        deployment_bucket=bucket,
                                        shard_uuid=shard_guid,
                                    )
                                ],
                            )
                        )
                    else:
                        k8_branch.append(
                            ShardDeploymentMetadata(
                                client_environment_name=f"{client}-{env}",
                                deployment_bucket=bucket,
                                shard_uuid=shard_guid,
                            )
                        )
            else:
                branches.append(
                    DeploymentBranchMetadata(
                        branch_suffix=f"legacy_{client}",
                        shard_files=[
                            ShardDeploymentMetadata(
                                client_environment_name=f"{client}-qa",
                                deployment_bucket=info,
                                shard_uuid=shard_guid,
                            )
                        ],
                    )
                )
        if k8_branch:
            branches.append(
                DeploymentBranchMetadata(
                    branch_suffix="k8",
                    shard_files=k8_branch,
                )
            )
        return branches

    def populate_attributes(self) -> None:
        """Populate instance attributes from the aetionpkg.shard.json file.

        Reads the shard package JSON file from S3 and extracts dataset metadata
        to populate instance attributes used throughout the deployment process.

        Raises:
            AirflowException: If the shard JSON file cannot be read or parsed.

        Example:
            For aetionpkg.shard.json containing:
            ```json
            {
                "name": "PATIENT_DATA",
                "tag": "CLIENT1",
                "revision": "20240101",
                "instanceNameOverride": "patient_data_v1"
            }
            ```

            Sets attributes:
            - self.name = "patient_data"
            - self.tag = "client1"
            - self.revision = "20240101"
            - self.versioned_path = "metaDataStore/versioned/patient_data/client1/20240101"
        """
        aetionpkg_shard_json_path = None
        try:
            aetionpkg_shard_json_path = os.path.join(
                self.full_shard_url, "shard", "aetionpkg.shard.json"
            )
            with self.fs.open(aetionpkg_shard_json_path, "r") as f:
                aetionpkg_shard = json.loads(f.read())
        except Exception as e:
            raise AirflowException(
                f"cannot read file: {aetionpkg_shard_json_path} - {e}"
            ) from e

        self.name = aetionpkg_shard["name"].lower()
        self.revision = aetionpkg_shard["revision"].lower()
        self.tag = aetionpkg_shard["tag"].lower()
        self.instance_name_override = aetionpkg_shard["instanceNameOverride"]
        self.versioned_path_base = f"{METADATA_VERSIONED_PREFIX}{self.name}/{self.tag}/"
        self.versioned_path = f"{self.versioned_path_base}{self.revision}"
        self.versioned_package_base_path = (
            f"{METADATA_VERSIONED_PREFIX}package/{self.name}/"
        )
        self.versioned_package_path = (
            f"{self.versioned_package_base_path}{self.tag}/{self.revision}"
        )
        self.report_template_file_name = f"{self.name}_datasetDescription.ftl"
        self.report_template_path = f"{REPORT_DIR}{self.name}"
        self.uuid_map_path = f"{REPORT_DIR}{UUID_MAP_CSV}"

    def get_package_files(
        self, branch: list[ShardDeploymentMetadata]
    ) -> list[PackageFileMetadata]:
        """Get package file metadata for deployment.

        Creates metadata objects for all package files that need to be deployed,
        including versioned metadata files and shard-specific package files.

        Args:
            branch: List of shard deployment metadata for the branch

        Returns:
            list[PackageFileMetadata]: List of package file metadata objects

        Raises:
            AttributeError: If populate_attributes() hasn't been called first

        Example:
            For a branch with 2 shards, returns metadata for:
            - db.json, ds.json, patientprofile.json (in versioned path)
            - aetionpkg.cs.json, aetionpkg.schema.json (in package path)
            - 2x aetionpkg.shard.json (one per shard with different UUIDs)
        """
        if self.versioned_path is None or self.versioned_package_path is None:
            raise AttributeError(
                "populate_attributes() must be called before get_package_files()"
            )

        package_files = [
            PackageFileMetadata("db.json", self.versioned_path),
            PackageFileMetadata("ds.json", self.versioned_path),
            PackageFileMetadata("patientprofile.json", self.versioned_path),
            PackageFileMetadata("aetionpkg.cs.json", self.versioned_package_path),
            PackageFileMetadata("aetionpkg.schema.json", self.versioned_package_path),
        ]

        for shard_file in branch:
            shard_json_path = f"shardpackage/{shard_file.client_environment_name}/{self.instance_name_override}"
            package_files.append(
                PackageFileMetadata(
                    "aetionpkg.shard.json",
                    shard_json_path,
                    shard_file.deployment_bucket,
                    shard_file.shard_uuid,
                )
            )

        return package_files

    def generate_report_template(
        self, package_file: PackageFileMetadata, tmp_meta_folder: str
    ) -> None:
        """Generate report template from dataset specification.

        Reads the dataset specification Excel file from S3 and extracts the report
        description to create a FreeMarker template file for the dataset.

        Args:
            package_file: Package file metadata containing template path and UUID
            tmp_meta_folder: Path to temporary metadata folder for file creation

        Raises:
            AirflowException: If spec file cannot be read or report description format is invalid

        Example:
            For dataset "patient_data", creates:
            {tmp_meta_folder}/reportTemplates/patient_data/patient_data_datasetDescription.ftl

            Content extracted from "Report Description" sheet, cell B3 of the Excel spec file.
            Expected format: "### Dataset Description\n\nThis dataset contains..."
        """
        logger.info("Trying to generate Report Template automatically from the spec")
        with self.fs.open(self.spec_s3_path, "rb") as f:
            report_description_sheet = pd.read_excel(f, sheet_name="Report Description")
        report_content = report_description_sheet.iloc[2, 1]

        # Ensure report_content is a string before checking startswith
        if not isinstance(report_content, str) or not report_content.startswith("### "):
            raise AirflowException(
                f"The spec is found in the artifacts folder {self.spec_s3_path}, "
                "but Report Description does not begin with '### '"
            )
        local_folder_path = Path(tmp_meta_folder, package_file.metadatastore_path)
        local_report_path = local_folder_path / package_file.filename
        os.makedirs(local_folder_path, exist_ok=True)
        with open(local_report_path, "w", encoding="UTF-8") as f:
            f.write(report_content)

        self.update_uuid_map_csv(package_file.shard_uuid_override, tmp_meta_folder)

    def update_uuid_map_csv(self, shard_uuid: str | None, tmp_meta_folder: str) -> None:
        """Update the UUID mapping CSV file with template UUID.

        Updates the uuidMap.csv file by appending an entry that maps the report template
        path to its corresponding UUID for report generation.

        Args:
            shard_uuid: UUID to associate with the report template
            tmp_meta_folder: Path to temporary metadata folder containing the CSV

        Raises:
            AssertionError: If any line in the CSV doesn't have exactly 2 columns

        Example:
            For dataset "patient_data" with UUID "uuid-1234", appends to uuidMap.csv:
            ```
            patient_data/patient_data_datasetDescription.ftl,uuid-1234
            ```
        """
        if shard_uuid is None:
            logger.warning("shard_uuid is None, skipping UUID map update")
            return

        local_uuid_map_path = Path(tmp_meta_folder, self.uuid_map_path)
        with open(local_uuid_map_path, "r") as f:
            reader = csv.reader(f)
            content = list(reader)
        content.append([f"{self.name}/{self.report_template_file_name}", shard_uuid])
        for line in content:
            assert len(line) == 2, (
                f"{local_uuid_map_path}: Found a line with invalid column number: '{line}'"
            )
        with open(local_uuid_map_path, "w") as f:
            writer = csv.writer(f, lineterminator="\n")
            writer.writerows(content)

    def execute(self, context: Context) -> None:
        """Execute the deploy package build operation.

        Main execution method that orchestrates the complete deployment workflow:
        1. Populate attributes from shard package JSON
        2. Validate deployment configuration
        3. Validate attribute prefix vs dataset name
        4. Validate transformation files exist and match S3
        5. Generate deployment branches for different environments
        6. Deploy package files to Git repository branches
        7. Create and push Git tags for deployment tracking

        Args:
            context: Airflow task execution context

        Raises:
            AirflowException: If any validation or deployment step fails
            AirflowFailException: If shard package validation fails

        Note:
            There are 3 paths in metadatastore git repo used for deployment package files:
            - package_path: metadatastore/shardpackage/
            - versioned_path: metadatastore/metaDataStore/versioned/
            - versioned_package_path: metadatastore/metaDataStore/versioned/package/
        """
        self.populate_attributes()

        self.__validate_deployment_config()
        self.__validate_attribute_prefix_vs_dataset_name()

        # package branch and error messages
        self.branches = self.get_branches()
        package_branch = f"dbc_{self.client}_{self.name}_{self.tag}_{self.revision}"
        message_base = f"{package_branch.upper()} (ADIP)"
        gdr_message = "\n\nGDR.SQL (hive_vars):\n" + self.fs.read_file_content(
            self.gdr_sql
        )
        shard_path_message = "\n\nShard path:\n" + os.path.join(
            "s3://" + self.full_shard_url, "shard/"
        )
        time_stamp = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S-%f")

        if self.gdr_validation is not None:
            gdr_validation_message = (
                "\n\nGDR-VALIDATION.JSON (validation_vars):\n"
                + self.fs.read_file_content(self.gdr_validation)
            )
            git_message = gdr_message + gdr_validation_message + shard_path_message
        else:
            git_message = gdr_message + shard_path_message

        # NOTE: we need a test guarding against sparse_directories
        client_environment_names = []
        for branch in self.branches:
            client_environment_names.extend(
                file.client_environment_name for file in branch.shard_files
            )
        sparse_directories = [
            self.versioned_path_base,
            self.versioned_package_base_path,
            REPORT_DIR,
        ]
        sparse_directories.extend(
            f"shardpackage/{client_env_name}/"
            for client_env_name in client_environment_names
        )

        with (
            tempfile.TemporaryDirectory() as tmp_con_folder,
            GitHook(
                self.git_creds.connector_repo, self.git_creds.private_key
            ) as con_git,
        ):
            logger.info(
                f"cloning connector {self.git_creds.connector_repo} ({self.git_creds.branch}) to {tmp_con_folder}"
            )
            con_git.clone(tmp_con_folder, self.git_creds.branch)
            self.__validate_if_files_are_in_master(tmp_con_folder)

            with (
                tempfile.TemporaryDirectory() as tmp_meta_folder,
                GitHook(
                    self.git_creds.metadatastore_repo, self.git_creds.private_key
                ) as meta_git,
            ):
                logger.info(
                    f"cloning metadatastore {self.git_creds.metadatastore_repo} to {tmp_meta_folder}"
                )
                meta_git.clone(
                    tmp_meta_folder,
                    self.git_creds.branch,
                    sparse_folders=sparse_directories,
                )
                self.__create_metadatastore_branches(
                    Path(tmp_meta_folder),
                    meta_git,
                    package_branch,
                    message_base,
                    git_message,
                    Path(self.versioned_path_base),
                )

                # tag connector master branch
                self.__tag_master_branch(
                    con_git,
                    package_branch.upper() + "-" + time_stamp,
                    message_base + git_message,
                )

                first_package_branch = (
                    package_branch + "_" + self.branches[0].branch_suffix
                )
                meta_git.repo.git.checkout(first_package_branch)
                self.deploy_hook.validate_report_template_exists(
                    tmp_meta_folder, self.name, self.tag, self.revision
                )

        if self.shard_package_exceptions:
            self.shard_package_exceptions.append(
                AirflowException(
                    "The metadatastore branch has been successfully created despite the above exceptions"
                )
            )
            raise ExceptionGroup("Test Failures", self.shard_package_exceptions)

    def _validate_shard_package_values(
        self,
        cur_shard_file_content: dict[str, Any],
        cur_shard_file_path: str | os.PathLike[str],
        prev_revision: str,
    ) -> None:
        """Validate shard package values against previous revision.

        Compares current shard package values with the previous revision to ensure
        critical values haven't changed inappropriately. Validates runtime directory
        consistency and sample shard configuration parameters.

        Args:
            cur_shard_file_content: Current shard package JSON content
            cur_shard_file_path: Path to current shard package file
            prev_revision: Previous revision identifier for comparison

        Raises:
            InvalidShardPackageValueException: If critical shard values have changed inappropriately

        Example:
            Validates that runtimeDir parent path remains consistent and that
            sampleShardNum/sampleShardPct don't increase between revisions.
        """
        logger.info("Validating shard package values")
        if not prev_revision:
            return
        prev_shard_file_path = str(cur_shard_file_path).replace(
            self.revision.upper(), prev_revision.upper()
        )
        if not Path(prev_shard_file_path).is_file():
            self.shard_package_exceptions.append(
                AirflowException(
                    f"Cannot find aetionpkg.shard.json for the previous revision (f{prev_revision}) "
                    f"in {prev_shard_file_path}. Probably due to changes in deployment_config. Make sure it was intentional"
                )
            )
            return
        with open(prev_shard_file_path, "r") as pkg_file:
            prev_shard_file_content = json.load(pkg_file)
        prev_runtimeDir = prev_shard_file_content["runtimeDir"]
        cur_runtimeDir = cur_shard_file_content["runtimeDir"]
        if Path(cur_runtimeDir).parent != Path(prev_runtimeDir).parent:
            self.shard_package_exceptions.append(
                InvalidShardPackageValueException(
                    changed_field="runtimeDir",
                    prev_value_path=prev_shard_file_path,
                    prev_value=prev_runtimeDir,
                    cur_value=cur_runtimeDir,
                )
            )
        for field in ("sampleShardNum", "sampleShardPct"):
            if cur_shard_file_content[field] > prev_shard_file_content[field]:
                self.shard_package_exceptions.append(
                    InvalidShardPackageValueException(
                        changed_field=field,
                        prev_value_path=prev_shard_file_path,
                        prev_value=prev_shard_file_content[field],
                        cur_value=cur_shard_file_content[field],
                    )
                )

    def __create_metadatastore_branches(
        self,
        tmp_meta_folder,
        git,
        package_branch,
        message_base,
        git_message,
        versioned_path_base,
    ):
        # create a separate branch and pull request for each runtime directory
        for branch in self.branches:
            package_branch_unique = package_branch + "_" + branch.branch_suffix
            git.checkout_new(package_branch_unique)

            # commit previous revision
            prev_revision = self.look_for_prev_revision(
                versioned_path_base, tmp_meta_folder
            )
            package_files = self.get_package_files(branch.shard_files)
            if prev_revision:
                for package_file in package_files:
                    current_rev_path = (
                        tmp_meta_folder
                        / package_file.metadatastore_path
                        / package_file.filename
                    )
                    prev_rev_path = Path(
                        str(current_rev_path).replace(self.revision, prev_revision)
                    )
                    if package_file.filename == "aetionpkg.shard.json":
                        prev_rev_path = Path(
                            str(current_rev_path).replace(
                                self.revision.upper(), prev_revision.upper()
                            )
                        )

                    if current_rev_path != prev_rev_path and prev_rev_path.is_file():
                        os.makedirs(
                            f"{tmp_meta_folder / package_file.metadatastore_path}",
                            exist_ok=True,
                        )
                        logger.info(
                            f"copying from the prev {prev_rev_path} to {package_file.metadatastore_path}"
                        )
                        shutil.copy(
                            prev_rev_path,
                            f"{tmp_meta_folder / package_file.metadatastore_path}",
                        )
                    else:
                        logger.warning(
                            f"the prev revision ({prev_revision}) file is not found at {prev_rev_path}"
                        )

                if git.any_changes():
                    message = (
                        message_base + f" [copy from the prev revision {prev_revision}]"
                    )
                    # add hive vars to message
                    prev_revision_gdr_sql = self.gdr_sql.replace(
                        self.revision, prev_revision
                    )
                    if self.fs.exists(prev_revision_gdr_sql):
                        message += (
                            "\n\nGDR.SQL (hive_vars):\n"
                            + self.fs.read_file_content(prev_revision_gdr_sql)
                        )
                    else:
                        logger.warning(
                            f"the prev revision gdr.sql is not found at {prev_revision_gdr_sql}"
                        )
                    git.commit(message)

            # read package files from s3
            for package_file in package_files:
                file_s3_path = os.path.join(
                    self.full_shard_url, "shard", package_file.filename
                )
                relative_file_local_path = os.path.join(
                    package_file.metadatastore_path, package_file.filename
                )
                file_local_path = os.path.join(
                    tmp_meta_folder, relative_file_local_path
                )
                os.makedirs(
                    os.path.join(tmp_meta_folder, package_file.metadatastore_path),
                    exist_ok=True,
                )

                # update guid and runtime dir in shard package json
                if package_file.filename == "aetionpkg.shard.json":
                    shard_file_content = self.deploy_hook.update_aetion_pkg_file(
                        file_s3_path=file_s3_path,
                        file_local_path=file_local_path,
                        package_file=package_file,
                        dataset_name=self.name,
                        revision=self.revision,
                        tag=self.tag,
                        etl_dir=self.full_shard_url,
                    )
                    self._validate_shard_package_values(
                        shard_file_content, file_local_path, prev_revision
                    )
                    self.deploy_hook.create_shard_metadata_json(
                        metadatastore_path=Path(tmp_meta_folder)
                        / versioned_path_base
                        / self.revision,
                        shard_package_data=shard_file_content,
                    )
                else:
                    logger.info(f"downloading {file_s3_path} to {file_local_path}")
                    self.fs.get(file_s3_path, file_local_path)

            if not self.deploy_hook.get_report(
                tmp_meta_folder, self.name, self.tag, self.revision
            ):
                logger.info(
                    "Report template was not found. Trying to add automatically..."
                )
                if not self.fs.exists(self.spec_s3_path):
                    logger.info(
                        f"The spec is not found in the artifacts folder {self.spec_s3_path}. "
                        "Cannot generate Report Template automatically."
                    )
                else:
                    package_file = PackageFileMetadata(
                        self.report_template_file_name,
                        self.report_template_path,
                        None,
                        branch.shard_files[0].shard_uuid,
                    )
                    self.generate_report_template(package_file, tmp_meta_folder)

            # commit and push
            git.commit(message_base + git_message)

            # git push -f -- we are replacing files and history in separate branch in case full shard rerun
            git.push(package_branch_unique, package_branch_unique, "-f")

            # safety check against accidental dataset name changes
            # NOTE: we lack a test that fails if this check gets moved to i.e.: validate_report_template_exists()
            self._validate_name_is_constant(tmp_meta_folder)

            self._notify_dds(
                branch=package_branch_unique,
                shard_metadata_path=Path(versioned_path_base)
                / self.revision
                / SHARD_METADATA_JSON,
            )

            git.repo.git.checkout(self.git_creds.branch)

    def look_for_prev_revision(self, versioned_path_base, local_metadatastore_repo_dir):
        """
        Find out whether a previous revision exists in a versioned tree (c.f.: utils.py::find_prev_revision())
        :param versioned_path_base: parent directory for all the revisions (i.e.: metaDataStore/versioned/name/tag/)
        :param new_revision: latest revision
        :return:
        """
        logger.info(f"looking for prev revision in {versioned_path_base}")
        local_path = local_metadatastore_repo_dir / versioned_path_base
        if not local_path.exists():
            logger.warning(
                f"{versioned_path_base} does not exist, so no previous revision found for {self.revision}"
            )
            return None

        prev_revision = self.revision_locator.find_prev_revision(
            self.revision, os.listdir(local_path)
        )

        if prev_revision:
            logger.info(
                f"the prev revision for {self.revision} may have been found: {prev_revision}"
            )
            return str(prev_revision)

    def _validate_name_is_constant(self, tmp_git):
        """
        Verify that the dataset name is not changed, not even in casing, between revisions. For finding the previous
        revision, it relies on the directory structure of the versioned/ tree of the metaDataStore following the convention:
            metaDataStore/versioned/dataset_name/tag_aka_client_aka_instance/revision
        :return: nothing on success (name is unchanged)
        :raises DatasetNameChangeException if the dataset's name does not match across versions
        """

        prev_revision = self.look_for_prev_revision(
            Path(self.versioned_path).parent, local_metadatastore_repo_dir=tmp_git
        )

        if prev_revision:
            files = [
                f
                for f in self.get_package_files([])
                if f.filename not in ["patientprofile.json", "db.json"]
            ]
            for f in files:
                self.__compare_dataset_name(f, prev_revision, tmp_git)

        else:
            logger.info(
                f"No previous revision for {self.versioned_path}, "
                f"looks like {self.name} is new for {self.client}."
            )

    def __compare_dataset_name(self, f: PackageFileMetadata, prev_revision, tmp_git):
        new_path = Path(f.metadatastore_path, f.filename)
        previous_path = Path(
            f.metadatastore_path.replace(self.revision, prev_revision), f.filename
        )
        with open(tmp_git / previous_path) as previous, open(tmp_git / new_path) as new:
            previous_name = json.load(previous)["name"]
            new_name = json.load(new)["name"]
            if previous_name != new_name:
                raise DatasetNameChangeException(
                    f.filename, previous_name, prev_revision, new_name, self.revision
                )
            else:
                logger.info(
                    f"dataset name in {f.filename} is {new_name} for both {prev_revision} and {self.revision}"
                )

    def _notify_dds(self, branch, shard_metadata_path):
        notify_dds(
            repo_url=self.git_creds.metadatastore_repo,
            branch=branch,
            customer=self.client,
            shard_metadata_path=shard_metadata_path,
            name=self.name,
            tag=self.tag,
            revision=self.revision,
            deploy=self.deployment_params.deploy,
            activate=self.deployment_params.deploy and self.deployment_params.activate,
        )
