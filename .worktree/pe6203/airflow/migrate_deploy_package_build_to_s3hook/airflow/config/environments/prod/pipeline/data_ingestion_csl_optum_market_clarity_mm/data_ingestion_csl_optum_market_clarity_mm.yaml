defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum_market_clarity_mm"
client: "csl"
revision: "20240130"
is_k8s: False
alert_user: "@rafal.kwiatkowski"
deployment_config:
  csl: "csl.aetion.com"
dynamic_patient_table: True

git_branch_override: "csl_optum_market_clarity_mm_20240123"
hive_vars:
  GDR_END_DATE: "2023-09-30"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/csl-databricks-etl-iam"
regenerate_all_flat_tables: True
transform_path: "humedica/optum_market_clarity_mm/csl/current"
upload_bucket: "s3://csl.aetion.com/upload/optum_market_clarity_mm/20240130"
use_smart_sampling: False
validation_path: "humedica/optum_market_clarity_mm/csl/current"
validation_vars:
  GDR_END_DATE: "2023-09-30"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 40
      min_workers: 40
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 10000
  full_patient_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 8000
  full_shard_job:
    autoscale:
      max_workers: 40
      min_workers: 1
  default:
    autoscale:
      max_workers: 20
      min_workers: 20
    node_type_id: "r-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
