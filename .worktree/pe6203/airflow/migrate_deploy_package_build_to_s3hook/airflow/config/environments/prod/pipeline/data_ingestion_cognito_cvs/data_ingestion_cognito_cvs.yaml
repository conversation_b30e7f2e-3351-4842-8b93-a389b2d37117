defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@nikolay.kharin"
client: "cognito"
dataset: "cvs"
revision: "20230726"
tag: "default"
rdc_dbc_name: cvs-default
upload_bucket: "cognito.aetion.com/upload/cvs/20230726"
deployment_config:
  cognito: "cognito.aetion.com"
hive_vars:
  GDR_END_DATE: "2021-12-31"
  GDR_START_DATE: "2014-01-03"
  INDEX_DT1: "2017-03-01"
  INDEX_DT2: "2016-08-30"
  INDEX_DT3: "2016-08-30"
validation_vars:
  GDR_END_DATE: "2021-12-31"
  GDR_START_DATE: "2014-01-03"
  INDEX_DT1: "2017-03-01"
  INDEX_DT2: "2016-08-30"
  INDEX_DT3: "2016-08-30"
transform_path: "cvs/cognito/current/"
validation_path: "cvs/cognito/current/"
dynamic_patient_table: true
git_branch_override: "remotes/origin/cognito-cvs-20230726"
iam_arn: "arn:aws:iam::627533566824:instance-profile/cognito-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: true
source_files_password: ""
use_smart_sampling: false
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.memory.offHeap.enabled: false
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 6000
      spark.task.cpus: 2
  default:
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.legacy.timeParserPolicy: "CORRECTED"
