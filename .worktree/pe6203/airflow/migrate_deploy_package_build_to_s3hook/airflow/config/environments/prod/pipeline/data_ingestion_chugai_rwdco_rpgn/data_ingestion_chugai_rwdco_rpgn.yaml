defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "rwdco_rpgn"
client: "chugai"
revision: "20240305"
is_k8s: False
alert_user: "@evgeniy.varganov"
deployment_config:
  chugai: "chugai.aetion.com"
dynamic_patient_table: True

git_branch_override: "chugai-rwdco_rpgn-20240305"
hive_vars:
  GDR_END_DATE: "2024-02-02"
  GDR_START_DATE: "1984-12-29"
iam_arn: "arn:aws:iam::627533566824:instance-profile/chugai-databricks-etl-iam"
regenerate_all_flat_tables: False
transform_path: "rwdco_rpgn/chugai/current"
upload_bucket: "chugai.aetion.com/upload/rwdco_rpgn/20240305"
validation_path: "rwdco_rpgn/chugai/current"
validation_vars:
  GDR_END_DATE: "2024-02-02"
  GDR_START_DATE: "1984-12-29"
steps_spark_config:
  default:
    autoscale:
      max_workers: 20
      min_workers: 1
    node_type_id: "c5.4xlarge"
    spark_conf:
      aetion.dataset.automaticNdcFormatting: False
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: False
      spark.databricks.io.cache.enabled: False
      spark.decommission.enabled: True
      spark.default.parallelism: 200
      spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.driver.maxResultSize: "20g"
      spark.executor.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.hadoop.fs.s3a.connection.maximum: 300
      spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
      spark.hadoop.fs.s3a.threads.max: 200
      spark.memory.offHeap.enabled: False
      spark.shuffle.io.maxRetries: 12
      spark.sql.adaptive.coalescePartitions.enabled: True
      spark.sql.adaptive.enabled: True
      spark.sql.adaptive.localShuffleReader.enabled: True
      spark.sql.adaptive.skewJoin.enabled: True
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: True
      spark.sql.shuffle.partitions: 200
      spark.storage.decommission.enabled: True
      spark.storage.decommission.rddBlocks.enabled: True
      spark.storage.decommission.shuffleBlocks.enabled: True
      spark.storage.decommission.shuffleBlocks.maxThreads: 32
