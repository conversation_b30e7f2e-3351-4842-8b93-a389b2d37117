defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "curimeta"
client: "curimeta"
revision: "20230615"
is_k8s: False
alert_user: "@nikolay.kharin"

git_branch_override: "curimeta-curimeta-rdc"
hive_vars:
  GDR_END_DATE: "2023-04-30"
  GDR_START_DATE: "2017-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/curimeta-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "symphony_all/jazz/current"
upload_bucket: "curimeta.aetion.com/upload/curimeta/20230615"
use_smart_sampling: False
validation_path: "symphony_all/jazz/current"
validation_vars:
  GDR_END_DATE: "2023-04-30"
  GDR_START_DATE: "2017-01-01"
steps_spark_config:
  full_job:
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 80
    spark_conf:
      spark.executor.heartbeatInterval: "600"
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.network.timeout: "1200"
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.task.cpus: 2
  default:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.default.parallelism: 4000
      spark.driver.maxResultSize: "0g"
      spark.memory.offHeap.enabled: False
      spark.network.timeout: "1200"
      spark.sql.autoBroadcastJoinThreshold: 209715200
      spark.sql.broadcastTimeout: 12000
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 2
      spark.task.maxDirectResultSize: 2097152000
  unarchiver_job_only:
    aws_attributes:
      ebs_volume_count: 1
      ebs_volume_size: 2048
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      zone_id: "auto"
    num_workers: 3
