import csv
import io
import json
import logging
import os
import pathlib
import shutil
from pathlib import Path
from unittest.mock import Mock

import pytest
import requests_mock
from airflow.exceptions import AirflowException
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
from dbc_utilities.dbc_validator.constants import (
    METADATA_VERSIONED_PREFIX,
    TRANSFORMATION_REPO_ROOT,
)
from exceptiongroup import ExceptionGroup
from git import Repo
from mypy_boto3_s3 import S3Client
from tools.config import DeploymentParameters
from tools.deploy_common import (
    REPORT_DIR,
    SHARD_METADATA_JSON,
    UUID_MAP_CSV,
    PackageFileMetadata,
    ShardDeploymentMetadata,
)

from dags.operators.deploy_package_build import (
    DatasetNameChangeException,
    DeployPackageBuild,
    InvalidShardPackageValueException,
)
from tests.spectools import assert_dds_test_payload


def create_deploy_package_build(
    full_shard_url: str = "tests/resources/attribute_prefix_validation/test1",
    dataset_artifacts_path: str = "dataset_artifacts_path",
    deployment_config: dict | None = None,
    repo: str = "repo",
    git_meta_repo: str = "git_meta_repo",
    transform_path: str = "transform_path",
    transform_path_override: str | None = None,
    data_transformation_url_s3: str = "data_transformation_url_s3",
    gdr_sql: str = "gdr_sql",
    gdr_validation: str = "gdr_validation",
    deployment_params: DeploymentParameters | None = None,
    client: str = "some_client",
):
    deployment_config = deployment_config if deployment_config is not None else {}
    return DeployPackageBuild(
        repo=repo,
        git_meta_repo=git_meta_repo,
        private_key="",
        git_default_branch="master",
        transform_path=transform_path,
        transform_path_override=transform_path_override,
        data_transformation_url_s3=data_transformation_url_s3,
        gdr_validation=gdr_validation,
        branch="master",
        client=client,
        gdr_sql=gdr_sql,
        task_id="deploy_package",
        dataset_artifacts_path=dataset_artifacts_path,
        deployment_config=deployment_config,
        deployment_params=deployment_params or DeploymentParameters(),
        full_shard_url=full_shard_url,
        resources="tests.resources",
    )


class TestPopulateAttributes:
    def test_populate_attributes_sets_fields_correctly(self):
        # Given: an operator with a mocked filesystem that returns a valid shard JSON
        operator = create_deploy_package_build(
            full_shard_url="s3://some-bucket/full-shard"
        )
        mock_fs = Mock()
        operator.fs = mock_fs  # type: ignore[attr-defined]

        shard_json = {
            "name": "DATASET_X",
            "tag": "CLIENT_A",
            "revision": "20240101",
            "instanceNameOverride": "DATASET_X_client_a_20240101",
        }
        mock_fs.open.return_value = io.StringIO(json.dumps(shard_json))

        # When: populate attributes is called
        operator.populate_attributes()

        # Then: core attributes are lower-cased and paths are built correctly
        assert operator.name == "dataset_x"
        assert operator.tag == "client_a"
        assert operator.revision == "20240101"
        assert operator.instance_name_override == "DATASET_X_client_a_20240101"

        expected_versioned_base = f"{METADATA_VERSIONED_PREFIX}dataset_x/client_a/"
        expected_versioned_path = expected_versioned_base + "20240101"
        assert operator.versioned_path_base == expected_versioned_base
        assert operator.versioned_path == expected_versioned_path

        expected_pkg_base = f"{METADATA_VERSIONED_PREFIX}package/dataset_x/"
        expected_pkg_path = expected_pkg_base + "client_a/20240101"
        assert operator.versioned_package_base_path == expected_pkg_base
        assert operator.versioned_package_path == expected_pkg_path

        assert operator.report_template_path == f"{REPORT_DIR}dataset_x"
        assert operator.report_template_file_name == "dataset_x_datasetDescription.ftl"
        assert operator.uuid_map_path == f"{REPORT_DIR}{UUID_MAP_CSV}"

    def test_populate_attributes_raises_when_read_fails(self):
        # Given
        full_shard_url = "s3://bucket/full-shard"
        operator = create_deploy_package_build(full_shard_url=full_shard_url)
        mock_fs = Mock()
        operator.fs = mock_fs  # type: ignore[attr-defined]
        mock_fs.open.side_effect = Exception("boom")

        # When / Then
        with pytest.raises(
            AirflowException, match=r"cannot read file: .*aetionpkg\.shard\.json"
        ):
            operator.populate_attributes()


class _TestBase:
    @pytest.fixture(autouse=True)
    def setup_s3_client(self, s3_client: S3Client, s3_hook: S3Hook):
        self.s3_client = s3_client
        self.s3_hook = s3_hook

    @pytest.fixture(autouse=True)
    def bucket(self, s3_client: S3Client):
        self.bucket_name = "some-bucket"
        s3_client.create_bucket(Bucket=self.bucket_name)
        yield self.bucket_name

    def upload_files_from(self, local_path: pathlib.Path, s3_path: str):
        for local_file in local_path.rglob("*"):
            if local_file.is_file():
                relative_path = local_file.relative_to(local_path)
                s3_key = f"{s3_path}/{relative_path}".lstrip("/")
                print(f"Uploading {local_file} to s3://{self.bucket_name}/{s3_key}")
                self.s3_client.upload_file(
                    Filename=str(local_file),
                    Bucket=self.bucket_name,
                    Key=s3_key,
                )

    @property
    def path_to_resources(self) -> pathlib.Path:
        return pathlib.Path("tests/resources")


class TestValidateAttributePrefixVsDatasetName(_TestBase):
    @pytest.mark.parametrize("test_dir, expected", [("test1", False), ("test2", True)])
    def test_validate_attribute_prefix_vs_dataset_name(
        self,
        test_dir: str,
        expected: bool,
    ):
        # Given some files in S3: aetionpkg.shard.json, db.json
        self.upload_files_from(
            local_path=pathlib.Path(
                f"tests/resources/attribute_prefix_validation/{test_dir}"
            ),
            s3_path="etl/dataset0/revision0/full-shard",
        )

        assert self.s3_hook.check_for_key(
            key="etl/dataset0/revision0/full-shard/shard/aetionpkg.shard.json",
            bucket_name=self.bucket_name,
        )

        assert self.s3_hook.check_for_key(
            key="etl/dataset0/revision0/full-shard/shard/db.json",
            bucket_name=self.bucket_name,
        )

        # And: the deploy-package-build operator pointing to that S3 location
        deploy_package = create_deploy_package_build(
            full_shard_url=f"s3://{self.bucket_name}/etl/dataset0/revision0/full-shard",
            client="client",
        )
        deploy_package.populate_attributes()

        # When: validating attribute prefix vs dataset name
        if expected:
            # Then:
            deploy_package._DeployPackageBuild__validate_attribute_prefix_vs_dataset_name()  # type: ignore[attr-defined]
        else:
            # Then: should raise AirflowException
            err_msg = "Attribute prefix in db.json does not match legacy attribute prefix. Please reach out to support."
            with pytest.raises(AirflowException, match=err_msg):
                deploy_package._DeployPackageBuild__validate_attribute_prefix_vs_dataset_name()  # type: ignore[attr-defined]


class TestDeployPackageBuildBranching(_TestBase):
    """Test class for branching and Git operations in DeployPackageBuild.

    This test class covers branch generation, Git tagging, and metadatastore
    branch creation functionality.
    """

    @pytest.fixture
    def deploy_package(self) -> DeployPackageBuild:
        """Create a DeployPackageBuild instance for testing.

        Returns:
            DeployPackageBuild: Configured instance with test parameters
        """
        package = create_deploy_package_build()
        package.name = "test_dataset"
        package.tag = "test_tag"
        package.revision = "20240101"
        return package

    def test_get_branches_mixed_deployment_config(
        self,
        deploy_package: DeployPackageBuild,
    ) -> None:
        """Test branch generation with mixed deployment configuration.

        This test validates that branches are correctly generated for a deployment
        configuration containing both legacy clients and Kubernetes environments.

        Args:
            deploy_package: DeployPackageBuild instance for testing
        """
        # Given: A deployment configuration with mixed client types
        deploy_package.deployment_config = {
            "client1": {"qa": "client1-qa-bucket", "prod": "client1-prod-bucket"},
            "client2": "client2-legacy-bucket",
            "client3": {"qa": "client3-qa-bucket", "prod": "client3-prod-bucket"},
        }

        # When: Getting branches for deployment
        branches = deploy_package.get_branches()

        # Then: Should create correct branch structure
        assert len(branches) == 4  # 3 legacy QA branches + 1 K8 branch

        # Check legacy QA branches
        legacy_branches = [b for b in branches if b.branch_suffix.startswith("legacy_")]
        assert len(legacy_branches) == 3
        assert any(b.branch_suffix == "legacy_client1" for b in legacy_branches)
        assert any(b.branch_suffix == "legacy_client2" for b in legacy_branches)
        assert any(b.branch_suffix == "legacy_client3" for b in legacy_branches)

        # Check K8 branch for prod environments
        k8_branches = [b for b in branches if b.branch_suffix == "k8"]
        assert len(k8_branches) == 1
        assert len(k8_branches[0].shard_files) == 2  # client1-prod and client3-prod

    def test_get_branches_legacy_only_config(
        self,
        deploy_package: DeployPackageBuild,
    ) -> None:
        """Test branch generation with legacy-only deployment configuration.

        This test validates that only legacy branches are created when all clients
        use the legacy string configuration format.

        Args:
            deploy_package: DeployPackageBuild instance for testing
        """
        # Given: A deployment configuration with only legacy clients
        deploy_package.deployment_config = {
            "legacy_client1": "bucket1",
            "legacy_client2": "bucket2",
        }

        # When: Getting branches for deployment
        branches = deploy_package.get_branches()

        # Then: Should create only legacy branches
        assert len(branches) == 2
        assert all(b.branch_suffix.startswith("legacy_") for b in branches)
        assert any(b.branch_suffix == "legacy_legacy_client1" for b in branches)
        assert any(b.branch_suffix == "legacy_legacy_client2" for b in branches)

    def test_get_branches_k8_only_config(
        self,
        deploy_package: DeployPackageBuild,
    ) -> None:
        """Test branch generation with Kubernetes-only deployment configuration.

        This test validates that only a K8 branch is created when all clients
        use the dictionary configuration format with prod environments.

        Args:
            deploy_package: DeployPackageBuild instance for testing
        """
        # Given: A deployment configuration with only K8 prod environments
        deploy_package.deployment_config = {
            "client1": {"prod": "client1-prod-bucket"},
            "client2": {"prod": "client2-prod-bucket"},
        }

        # When: Getting branches for deployment
        branches = deploy_package.get_branches()

        # Then: Should create only K8 branch
        assert len(branches) == 1
        assert branches[0].branch_suffix == "k8"
        assert len(branches[0].shard_files) == 2

    def test_get_branches_empty_config(
        self,
        deploy_package: DeployPackageBuild,
    ) -> None:
        """Test branch generation with empty deployment configuration.

        This test validates that no branches are created when the deployment
        configuration is empty.

        Args:
            deploy_package: DeployPackageBuild instance for testing
        """
        # Given: An empty deployment configuration
        deploy_package.deployment_config = {}

        # When: Getting branches for deployment
        branches = deploy_package.get_branches()

        # Then: Should create no branches
        assert len(branches) == 0

    def test_get_branches(self):
        full_shard_path = "etl/dataset0/revision0/full-shard"
        full_shard_url = os.path.join("s3://", self.bucket_name, full_shard_path)
        self.upload_files_from(
            local_path=self.path_to_resources / "attribute_prefix_validation" / "test1",
            s3_path=full_shard_path,
        )

        deploy_config = {
            "client1": "bucket1",
            "client2": {"qa": "bucket2"},
            "client3": {"cqa": "bucket3"},
            "client4": {"prod": "bucket4"},
            "client5": {"qa": "bucket5", "cqa": "bucket6", "prod": "bucket7"},
            "client6": {"qa": "bucket8", "cqa": "bucket9", "prod": "bucket10"},
        }
        deploy_package = create_deploy_package_build(
            full_shard_url=full_shard_url,
            deployment_config=deploy_config,
        )
        deploy_package.populate_attributes()

        actual = deploy_package.get_branches()
        expected = {
            "client1-qa": {"bucket": "bucket1", "name": "legacy_client1"},
            "client2-qa": {"bucket": "bucket2", "name": "legacy_client2"},
            "client3-cqa": {"bucket": "bucket3", "name": "k8"},
            "client4-prod": {"bucket": "bucket4", "name": "k8"},
            "client5-qa": {"bucket": "bucket5", "name": "legacy_client5"},
            "client5-cqa": {"bucket": "bucket6", "name": "k8"},
            "client5-prod": {"bucket": "bucket7", "name": "k8"},
            "client6-qa": {"bucket": "bucket8", "name": "legacy_client6"},
            "client6-cqa": {"bucket": "bucket9", "name": "k8"},
            "client6-prod": {"bucket": "bucket10", "name": "k8"},
        }
        expected_guids = {}
        assert len(actual) == 5
        for i in actual:
            for shard_file in i.shard_files:
                env = shard_file.client_environment_name
                assert shard_file.deployment_bucket == expected[env]["bucket"]
                assert i.branch_suffix == expected[env]["name"]
                expected_guids[env] = shard_file.shard_uuid

        assert (
            expected_guids["client5-qa"]
            == expected_guids["client5-cqa"]
            == expected_guids["client5-prod"]
        )
        assert (
            expected_guids["client6-qa"]
            == expected_guids["client6-cqa"]
            == expected_guids["client6-prod"]
        )
        assert (
            expected_guids["client1-qa"]
            != expected_guids["client2-qa"]
            != expected_guids["client3-cqa"]
            != expected_guids["client4-prod"]
            != expected_guids["client5-qa"]
            != expected_guids["client6-qa"]
        )

    def test_tag_master_branch_success(
        self,
        deploy_package: DeployPackageBuild,
    ) -> None:
        """Test successful Git tagging of master branch.

        This test validates that the master branch is correctly tagged with
        deployment information.

        Args:
            deploy_package: DeployPackageBuild instance for testing
        """
        # Given: A mock Git hook and tag information
        mock_git = Mock()
        mock_repo = Mock()
        mock_tag = Mock()
        mock_remote = Mock()

        mock_git.repo = mock_repo
        mock_repo.create_tag.return_value = mock_tag
        mock_repo.remotes.origin = mock_remote

        tag_name = (
            "DBC_CLIENT1_TEST_DATASET_TEST_TAG_20240101-2024-01-01-12-00-00-123456"
        )
        tag_message = "DBC_CLIENT1_TEST_DATASET_TEST_TAG_20240101 (ADIP)\n\nGDR.SQL: SELECT * FROM test;"

        # When: Tagging the master branch
        deploy_package._DeployPackageBuild__tag_master_branch(  # type: ignore[attr-defined]
            mock_git, tag_name, tag_message
        )

        # Then: Should create and push the tag
        mock_repo.create_tag.assert_called_once_with(tag_name, message=tag_message)
        mock_remote.push.assert_called_once_with(mock_tag)

    def test_tag_master_branch_push_failure(
        self,
        deploy_package: DeployPackageBuild,
    ) -> None:
        """Test Git tagging when push operation fails.

        This test validates that exceptions during tag pushing are properly propagated.

        Args:
            deploy_package: DeployPackageBuild instance for testing
        """
        # Given: A mock Git hook that fails during push
        mock_git = Mock()
        mock_repo = Mock()
        mock_tag = Mock()
        mock_remote = Mock()

        mock_git.repo = mock_repo
        mock_repo.create_tag.return_value = mock_tag
        mock_repo.remotes.origin = mock_remote
        mock_remote.push.side_effect = Exception("Push failed")

        tag_name = "TEST_TAG"
        tag_message = "Test message"

        # When/Then: Tagging should raise exception
        with pytest.raises(Exception, match="Push failed"):
            deploy_package._DeployPackageBuild__tag_master_branch(  # type: ignore[attr-defined]
                mock_git, tag_name, tag_message
            )


class TestDeployPackageBuildLookForPrevRevision:
    """Comprehensive test class for the look_for_prev_revision method.

    This test class covers all scenarios for finding previous revisions in the
    metadatastore directory structure, including happy paths, edge cases, and error conditions.
    """

    @pytest.fixture
    def deploy_package(self) -> DeployPackageBuild:
        """Create a DeployPackageBuild instance for testing.

        Returns:
            DeployPackageBuild: Configured instance with test parameters
        """
        return create_deploy_package_build()

    @pytest.fixture(autouse=True)
    def setup_s3_client(self, s3_client: S3Client):
        """Setup S3 client for testing."""
        self.s3_client = s3_client

    def test_look_for_prev_revision_happy_path_single_previous(
        self,
        deploy_package: DeployPackageBuild,
        tmp_path: Path,
    ) -> None:
        """Test finding previous revision when exactly one exists.

        This test validates the happy path scenario where there is exactly one
        previous revision available in the versioned directory structure.

        Args:
            deploy_package: DeployPackageBuild instance for testing
            tmp_path: Temporary directory for test files
        """
        # Given: A versioned directory structure with one previous revision
        deploy_package.revision = "20240201"
        versioned_path_base = Path("metaDataStore/versioned/dataset1/tag1")
        local_repo_dir = tmp_path / "metadatastore"

        # Create directory structure with previous revision
        revision_dir = local_repo_dir / versioned_path_base
        revision_dir.mkdir(parents=True)
        (revision_dir / "20240101").mkdir()  # Previous revision

        # When: Looking for previous revision
        result = deploy_package.look_for_prev_revision(
            versioned_path_base, local_repo_dir
        )

        # Then: Should return the previous revision as string
        assert result == "20240101"

    def test_look_for_prev_revision_happy_path_multiple_previous(
        self,
        deploy_package: DeployPackageBuild,
        tmp_path: Path,
    ) -> None:
        """Test finding previous revision when multiple exist.

        This test validates that the method correctly identifies the most recent
        previous revision when multiple previous revisions are available.

        Args:
            deploy_package: DeployPackageBuild instance for testing
            tmp_path: Temporary directory for test files
        """
        # Given: A versioned directory structure with multiple previous revisions
        deploy_package.revision = "20240301"
        versioned_path_base = Path("metaDataStore/versioned/dataset1/tag1")
        local_repo_dir = tmp_path / "metadatastore"

        # Create directory structure with multiple previous revisions
        revision_dir = local_repo_dir / versioned_path_base
        revision_dir.mkdir(parents=True)
        (revision_dir / "20240101").mkdir()  # Older revision
        (revision_dir / "20240201").mkdir()  # More recent revision
        (revision_dir / "20240215").mkdir()  # Most recent revision

        # When: Looking for previous revision
        result = deploy_package.look_for_prev_revision(
            versioned_path_base, local_repo_dir
        )

        # Then: Should return the most recent previous revision
        assert result == "20240215"

    def test_look_for_prev_revision_no_previous_revisions(
        self,
        deploy_package: DeployPackageBuild,
        tmp_path: Path,
    ) -> None:
        """Test behavior when no previous revisions exist.

        This test validates that the method returns None when the versioned
        directory exists but contains no previous revisions.

        Args:
            deploy_package: DeployPackageBuild instance for testing
            tmp_path: Temporary directory for test files
        """
        # Given: A versioned directory structure with no previous revisions
        deploy_package.revision = "20240101"
        versioned_path_base = Path("metaDataStore/versioned/dataset1/tag1")
        local_repo_dir = tmp_path / "metadatastore"

        # Create empty directory structure
        revision_dir = local_repo_dir / versioned_path_base
        revision_dir.mkdir(parents=True)

        # When: Looking for previous revision
        result = deploy_package.look_for_prev_revision(
            versioned_path_base, local_repo_dir
        )

        # Then: Should return None
        assert result is None

    def test_look_for_prev_revision_directory_does_not_exist(
        self,
        deploy_package: DeployPackageBuild,
        tmp_path: Path,
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        """Test behavior when versioned directory does not exist.

        This test validates that the method returns None and logs a warning
        when the versioned path base directory does not exist.

        Args:
            deploy_package: DeployPackageBuild instance for testing
            tmp_path: Temporary directory for test files
        """
        # Given: A non-existent versioned directory path
        deploy_package.revision = "20240101"
        versioned_path_base = Path("metaDataStore/versioned/nonexistent/tag1")
        local_repo_dir = tmp_path / "metadatastore"

        # When: Looking for previous revision in non-existent directory
        with caplog.at_level(logging.WARNING):
            result = deploy_package.look_for_prev_revision(
                versioned_path_base, local_repo_dir
            )

        # Then: Should return None without calling revision locator
        assert result is None

        # And: Should log a warning about the missing directory
        assert (
            "metaDataStore/versioned/nonexistent/tag1 does not exist, so no previous revision found for 20240101"
            in caplog.text
        )

    def test_look_for_prev_revision_empty_directory(
        self,
        deploy_package: DeployPackageBuild,
        tmp_path: Path,
    ) -> None:
        """Test behavior when versioned directory exists but is empty.

        This test validates that the method returns None when the versioned
        directory exists but contains no subdirectories.

        Args:
            deploy_package: DeployPackageBuild instance for testing
            tmp_path: Temporary directory for test files
        """
        # Given: An empty versioned directory
        deploy_package.revision = "20240101"
        versioned_path_base = Path("metaDataStore/versioned/dataset1/tag1")
        local_repo_dir = tmp_path / "metadatastore"

        # Create empty directory structure
        revision_dir = local_repo_dir / versioned_path_base
        revision_dir.mkdir(parents=True)

        # When: Looking for previous revision in empty directory
        result = deploy_package.look_for_prev_revision(
            versioned_path_base, local_repo_dir
        )

        # Then: Should return None
        assert result is None

    @pytest.mark.parametrize(
        "current_revision,directory_revisions,expected_result",
        [
            # Standard date format scenarios
            ("20240301", ["20240101", "20240201"], "20240201"),
            ("20240301", ["20240101"], "20240101"),
            ("20240301", [], None),
            # Mixed format scenarios
            ("20240301", ["20240101", "20240201_v1", "2024021501"], "2024021501"),
            ("client20240301", ["client20240101", "client20240201"], "client20240201"),
            # Edge cases with version suffixes
            ("20240301", ["2024010101", "2024010102", "20240201"], "20240201"),
            ("2024030101", ["20240101", "20240201", "2024021501"], "2024021501"),
            # No valid previous revisions
            ("20240101", ["20240201", "20240301"], None),
            ("20240101", ["invalid", "not_a_revision"], None),
        ],
    )
    def test_look_for_prev_revision_parametrized_scenarios(
        self,
        deploy_package: DeployPackageBuild,
        tmp_path: Path,
        current_revision: str,
        directory_revisions: list[str],
        expected_result: str | None,
    ) -> None:
        """Parametrized test for various revision scenarios.

        This test validates the method behavior across multiple scenarios using
        parametrized test data to ensure comprehensive coverage of different
        revision patterns and edge cases.

        Args:
            deploy_package: DeployPackageBuild instance for testing
            tmp_path: Temporary directory for test files
            current_revision: The current revision to find previous for
            directory_revisions: List of revision directories to create
            expected_result: Expected previous revision result
        """
        # Given: A versioned directory with specified revisions
        deploy_package.revision = current_revision
        versioned_path_base = Path("metaDataStore/versioned/dataset1/tag1")
        local_repo_dir = tmp_path / "metadatastore"

        # Create directory structure with specified revisions
        revision_dir = local_repo_dir / versioned_path_base
        revision_dir.mkdir(parents=True)
        for revision in directory_revisions:
            (revision_dir / revision).mkdir()

        # When: Looking for previous revision (using real RevisionLocator)
        result = deploy_package.look_for_prev_revision(
            versioned_path_base, local_repo_dir
        )

        # Then: Should return expected result
        assert result == expected_result


class TestGetPackageFiles(_TestBase):
    @pytest.mark.parametrize(
        "branch, expected_shard",
        [
            (
                [
                    ShardDeploymentMetadata(
                        client_environment_name="client1-qa",
                        deployment_bucket="bucket1",
                        shard_uuid="guid1",
                    )
                ],
                [
                    PackageFileMetadata(
                        "aetionpkg.shard.json",
                        "shardpackage/client1-qa/TEST1_DEFAULT_20211022",
                        "bucket1",
                        "guid1",
                    )
                ],
            ),
            (
                [
                    ShardDeploymentMetadata(
                        client_environment_name="client1-cqa",
                        deployment_bucket="bucket1",
                        shard_uuid="guid1",
                    ),
                    ShardDeploymentMetadata(
                        client_environment_name="client1-prod",
                        deployment_bucket="bucket2",
                        shard_uuid="guid1",
                    ),
                ],
                [
                    PackageFileMetadata(
                        "aetionpkg.shard.json",
                        "shardpackage/client1-cqa/TEST1_DEFAULT_20211022",
                        "bucket1",
                        "guid1",
                    ),
                    PackageFileMetadata(
                        "aetionpkg.shard.json",
                        "shardpackage/client1-prod/TEST1_DEFAULT_20211022",
                        "bucket2",
                        "guid1",
                    ),
                ],
            ),
        ],
    )
    def test_get_package_files(self, branch: str, expected_shard: str):
        full_shard_path = "etl/dataset0/revision0/full-shard"
        full_shard_url = os.path.join("s3://", self.bucket_name, full_shard_path)
        self.upload_files_from(
            local_path=self.path_to_resources / "attribute_prefix_validation" / "test1",
            s3_path=full_shard_path,
        )

        deploy_package = create_deploy_package_build(
            full_shard_url=full_shard_url,
        )
        deploy_package.populate_attributes()

        expected = [
            PackageFileMetadata(
                "db.json", "metaDataStore/versioned/test1/default/20211022"
            ),
            PackageFileMetadata(
                "ds.json", "metaDataStore/versioned/test1/default/20211022"
            ),
            PackageFileMetadata(
                "patientprofile.json", "metaDataStore/versioned/test1/default/20211022"
            ),
            PackageFileMetadata(
                "aetionpkg.cs.json",
                "metaDataStore/versioned/package/test1/default/20211022",
            ),
            PackageFileMetadata(
                "aetionpkg.schema.json",
                "metaDataStore/versioned/package/test1/default/20211022",
            ),
        ]
        expected += expected_shard
        actual = deploy_package.get_package_files(branch)
        assert actual == expected


class TestGenerateReportTemplate(_TestBase):
    def test_generate_report_template(self, tmp_path: pathlib.Path):
        # Given: some files in S3: aetionpkg.shard.json, db.json
        full_shard_path = "etl/dataset0/revision0/full-shard"
        full_shard_url = os.path.join("s3://", self.bucket_name, full_shard_path)
        self.upload_files_from(
            local_path=self.path_to_resources / "attribute_prefix_validation" / "test1",
            s3_path=full_shard_path,
        )

        # And: the data_specification is in the artifacts folder
        self.s3_client.upload_file(
            Filename=str(self.path_to_resources / "data_specification.xlsx"),
            Bucket=self.bucket_name,
            Key="etl/dataset0/revision0/artifacts/data_specification.xlsx",
        )

        # And: the deploy-package-build operator pointing to that S3 location
        mock_dataset_artifacts_path = (
            f"s3://{self.bucket_name}/etl/dataset0/revision0/artifacts/"
        )
        deploy_package = create_deploy_package_build(
            dataset_artifacts_path=mock_dataset_artifacts_path,
            full_shard_url=full_shard_url,
        )

        # And: the deploy-packagebuild operator has been configured
        deploy_package.populate_attributes()

        package_file = PackageFileMetadata(
            deploy_package.report_template_file_name,
            deploy_package.report_template_path,
            None,
            "test-non-random-fake-uuid",
        )

        tmp_report_tmpl_dir = str(tmp_path)
        path_to_metadatastore = self.path_to_resources / "metadatastore"
        assert (path_to_metadatastore / "reportTemplates").is_dir(), (
            f"Test setup error: {path_to_metadatastore / 'reportTemplates'} should be a directory"
        )
        shutil.copytree(
            path_to_metadatastore / "reportTemplates",
            Path(tmp_report_tmpl_dir, "reportTemplates"),
        )

        # When: generating the report template
        deploy_package.generate_report_template(package_file, tmp_report_tmpl_dir)

        local_report_path = Path(
            tmp_report_tmpl_dir, package_file.metadatastore_path, package_file.filename
        )
        assert local_report_path.is_file(), (
            f"Report template file should be created at path tmp_meta_dir/{Path(package_file.metadatastore_path, package_file.filename)}"
        )

        with open(local_report_path) as f:
            report_template_content = f.read()
        assert report_template_content.startswith("### "), (
            "Report template should start with '### '"
        )

        original_uuid_map_path = path_to_metadatastore / deploy_package.uuid_map_path
        with open(original_uuid_map_path) as f:
            original_length = sum(1 for _ in csv.reader(f))
        updated_uuid_map_path = Path(tmp_report_tmpl_dir, deploy_package.uuid_map_path)
        with open(updated_uuid_map_path) as f:
            updated_uuid_map = list(csv.reader(f))
        updated_length = len(updated_uuid_map)
        assert updated_length == original_length + 1, (
            "One row should be appended to uuidMap.csv"
        )

        report_path = (
            f"{deploy_package.name}/{deploy_package.report_template_file_name}"
        )
        added_path, added_uuid = updated_uuid_map[-1]
        assert (
            report_path == added_path and package_file.shard_uuid_override == added_uuid
        )


class TestValidateShardPackageValues(_TestBase):
    @pytest.mark.parametrize(
        "runtimeDir, sampleShardNum, sampleShardPct, changed_field, shard_dir",
        [
            (
                "s3://mock.local/runtime/dataset_7/instance_a/20220103/",
                251,
                0.05,
                None,
                "test-qa",
            ),
            (
                "s3://mock.local/runtime/wrong_dataset_7/instance_a/20220103/",
                251,
                0.05,
                "runtimeDir",
                "test-qa",
            ),
            (
                "s3://mock.local/runtime/dataset_7/instance_a/20220103/",
                300,
                0.05,
                "sampleShardNum",
                "test-qa",
            ),
            (
                "s3://mock.local/runtime/dataset_7/instance_a/20220103/",
                251,
                0.5,
                "sampleShardPct",
                "test-qa",
            ),
            (
                "s3://mock.local/runtime/dataset_7/instance_a/20220103/",
                251,
                0.5,
                None,
                "wrong_dir-qa",
            ),
        ],
    )
    def test_validate_shard_package_values(
        self,
        runtimeDir: str,
        sampleShardNum: int,
        sampleShardPct: float,
        changed_field: str | None,
        shard_dir: str,
    ):
        # Given: some files in S3: aetionpkg.shard.json
        full_shard_path = "etl/dataset_7/20220103/full-shard"
        self.upload_files_from(
            local_path=self.path_to_resources / "s3" / "20220103" / "full-shard",
            s3_path=full_shard_path,
        )
        full_shard_url = os.path.join("s3://", self.bucket_name, full_shard_path)

        # And: the deploy-package-build operator pointing to that S3 location
        deploy_package = create_deploy_package_build(
            dataset_artifacts_path="mock_dataset_artifacts_path",
            full_shard_url=full_shard_url,
        )

        # And: the deploy-package-build operator has been configured
        deploy_package.populate_attributes()

        # And: the current shard package file content to validate
        cur_shard_file_content = {
            "name": "DATASET_7",
            "revision": "20220103",
            "tag": "instance_a",
            "instanceNameOverride": "DATASET_7_instance_a_20220103",
            "runtimeDir": runtimeDir,
            "patientNum": 42,
            "sampleShardNum": sampleShardNum,
            "sampleShardPct": sampleShardPct,
            "supportSampleData": True,
            "sampleDataPatientNum": 1,
            "__comment__": "rest of fields omitted",
            "shardGUID": "7dc9eea9-5d0f-3495-2bc3-0797a7d524a9",
        }
        file_local_path = (
            self.path_to_resources
            / f"metadatastore/shardpackage/{shard_dir}/DATASET_7_instance_a_20220103/aetionpkg.shard.json"
        )
        prev_revision = "20220102"

        # When: validating the shard package values
        deploy_package._validate_shard_package_values(
            cur_shard_file_content, str(file_local_path), prev_revision
        )
        # Then: the expected exception is raised if there is a change in the field
        if changed_field or shard_dir != "test-qa":
            assert len(deploy_package.shard_package_exceptions) == 1
            first_exception = deploy_package.shard_package_exceptions[0]
            if changed_field:
                assert isinstance(first_exception, InvalidShardPackageValueException)
                assert first_exception.changed_field == changed_field
                assert changed_field in str(first_exception)
            elif shard_dir != "test-qa":
                assert isinstance(first_exception, AirflowException)
                assert "aetionpkg.shard.json" in str(first_exception)
        else:
            assert len(deploy_package.shard_package_exceptions) == 0


class TestConstantName(_TestBase):
    @pytest.mark.parametrize(
        "dataset, client, revision, expected",
        [
            ("dataset_7", "instance_a", "20220101", None),  # no previous revision
            (
                "dataset_7",
                "instance_a",
                "20220102",
                ("ds.json", "DaTaSeT_7", "20220101", "DATASET_7", "20220102"),
            ),
            ("dataset_7", "instance_a", "20220103", None),  # nominal case
            (
                "dataset_8",
                "instance_b",
                "20220224",
                # "dataset_8 at 20220224 does not match dAtAsEt_8 at 20220212 in aetionpkg.cs.json",
                ("aetionpkg.cs.json", "dAtAsEt_8", "20220212", "dataset_8", "20220224"),
            ),
            (
                "dataset_8",
                "instance_b",
                "20220229",
                # "dAtAsEt_8 at 20220229 does not match dataset_8 at 20220224 in aetionpkg.schema.json",
                (
                    "aetionpkg.schema.json",
                    "dataset_8",
                    "20220224",
                    "dAtAsEt_8",
                    "20220229",
                ),
            ),
        ],
        ids=[
            "no_previous_revision",
            "changed_in_schema_case_mixed",
            "nominal_case",
            "changed_in_cs",
            "changed_in_schema",
        ],
    )
    def test_constant_name(
        self,
        dataset: str,
        client: str,
        revision: str,
        expected: str | tuple | None,
        tmp_path: pathlib.Path,
    ):
        # Given: the local copy of the metadatastore repo
        mock_mds_repo_dir = str(self.path_to_resources / "metadatastore")
        # aetionpkg.shard.json is used to drive the dataset_name / tag a.k.a. client a.k.a instance / revision discovery
        # (c.f. DeployPackageBuild::populate_attributes). Instead of adding a bunch of directories & files to the
        # tests/resources fixtures, just create an appropriate one ad-hoc (could also be factored out to a fixture)

        # And: aetionpkg.shard.json in S3
        full_shard_path = f"etl/{dataset}/{revision}/full-shard"
        full_shard_url = os.path.join("s3://", self.bucket_name, full_shard_path)
        aetionpkg_shard_json_content = f'''{{
            "name": "{dataset.upper()}",
            "revision": "{revision}",
            "tag": "{client}",
            "instanceNameOverride": "{dataset.upper()}_{client}_{revision}",
            "__comment__": "rest of fields omitted"
            }}'''

        self.s3_client.put_object(
            Bucket=self.bucket_name,
            Key=os.path.join(full_shard_path, "shard", "aetionpkg.shard.json"),
            Body=aetionpkg_shard_json_content,
        )

        deploy_package = create_deploy_package_build(
            full_shard_url=full_shard_url,
            deployment_config=None,
        )

        # And: the deploy-package-build operator has been configured
        deploy_package.populate_attributes()

        # When: there is change in the dataset name in one of the metadata files in the previous revision
        if expected:
            with pytest.raises(expected_exception=DatasetNameChangeException) as e:
                # Then: an exception is raised
                deploy_package._validate_name_is_constant(mock_mds_repo_dir)
            assert e.value.args == expected
        else:
            # When/Then: no exception is raised
            deploy_package._validate_name_is_constant(mock_mds_repo_dir)


class TestExecute(_TestBase):
    @pytest.fixture(autouse=True)
    def _patch_set_dd_url(self, monkeypatch):
        monkeypatch.setenv("DDS_URL", "http://dds.invalid")
        yield

    @pytest.fixture(autouse=True)
    def _patch_local_connector(self, local_connector: str):
        self.local_connector = local_connector

    @pytest.fixture(autouse=True)
    def _patch_metadatastore_repo(self, request: pytest.FixtureRequest):
        from tests.spectools import f_metadatastore_repo

        revision = request.node.callspec.params.get("revision")
        with f_metadatastore_repo(revision) as metadatastore_repo:
            self.metadatastore_repo = metadatastore_repo
            yield

    @pytest.fixture(autouse=True)
    def _patch_s3_transform_dir(
        self,
        request: pytest.FixtureRequest,
        local_connector: str,
        setup_s3_client,
        bucket: str,
    ):
        s3_transform_path = request.node.callspec.params.get("transform_path")
        s3_transform_path_override = request.node.callspec.params.get(
            "transform_path_override"
        )
        revision = request.node.callspec.params.get("revision")
        s3_transform_dir = f"etl/dataset0/{revision}/transform"
        self.upload_files_from(
            Path(local_connector) / TRANSFORMATION_REPO_ROOT / s3_transform_path,
            s3_transform_dir,
        )
        if s3_transform_path_override:
            self.upload_files_from(
                Path(local_connector)
                / TRANSFORMATION_REPO_ROOT
                / s3_transform_path_override,
                s3_transform_dir,
            )
        self.s3_client.put_object(
            Bucket=self.bucket_name, Key=f"{s3_transform_dir}/gdr.sql", Body=b""
        )
        self.s3_transform_dir = os.path.join(
            "s3://", self.bucket_name, s3_transform_dir
        )
        yield

    @pytest.fixture(autouse=True)
    def _mock_requests(self):
        with requests_mock.Mocker(real_http=True) as mock:
            mock.post(
                "http://dds.invalid/v1/customers/some_client/environments/prod/ingestion"
            )
            self.mock = mock
            yield

    @pytest.mark.parametrize(
        "revision, transform_path, transform_path_override, shard_guid, commits_number, touched_files_number, sampleShardPct",
        [
            (
                "20220101",
                "test_dbc",
                None,
                "7dc9eea9-5d0f-3495-90ee-b5f7a7d524a9",
                2,
                9,
                0.05,
            ),
            (
                "20220103",
                "test_dbc",
                None,
                "7dc9eea9-5d0f-3495-2bc3-0797a7d524a9",
                3,
                7,
                0.1,
            ),
        ],
    )
    def test_execute(
        self,
        revision: str,
        transform_path: str,
        transform_path_override: str | None,
        shard_guid: str,
        commits_number: int,
        touched_files_number: int,
        sampleShardPct: float,
    ):
        """Verifies the end-to-end execution of the DeployPackageBuild operator.

        This test simulates a full run of the operator, covering the entire process
        from reading input data to creating Git artifacts and making external API calls.
        It validates that the operator correctly:
        1.  Creates and populates a new branch in the metadatastore repository with
            the correct package files (e.g., `aetionpkg.shard.json`, `ds.json`).
        2.  Generates the correct content and structure for the committed files.
        3.  Creates the expected number of commits and modifies the correct files.
        4.  Tags the connector repository to mark the deployment.
        5.  Calls the Dataset Deployment Service (DDS) with the correct payload to
            trigger the deployment.
        6.  Handles expected exceptions for specific scenarios (e.g., revision '20220103').

        Args:
            revision (str): The dataset revision ID, e.g., "20220101". This
                controls which test data fixture is used.
            transform_path (str): The path to transformation files within the
                connector repository, e.g., "test_dbc".
            transform_path_override (str | None): An optional path for overriding
                transformation files.
            shard_guid (str): The expected GUID for the shard in the generated
                metadata.
            commits_number (int): The expected number of commits to be created in
                the metadatastore branch.
            touched_files_number (int): The expected number of files that should be
                created or modified in the metadatastore branch.
            sampleShardPct (float): The expected value for the `sampleShardPct`
                field in the final `aetionpkg.shard.json`.
        """

        # Given: shard-stage artifacts in S3 like aetionpkg.shard.json
        full_shard_path = f"etl/dataset0/{revision}/full-shard"
        self.upload_files_from(
            local_path=self.path_to_resources / "s3" / revision / "full-shard",
            s3_path=full_shard_path,
        )
        full_shard_url = os.path.join("s3://", self.bucket_name, full_shard_path)

        # GIVEN: A mocked metadatastore repository and S3 test data environment
        assert self.metadatastore_repo

        # GIVEN: A mocked etl configuration in S3
        assert self.s3_transform_dir

        # GIVEN: A mocked DDS endpoint
        assert self.mock

        # GIVEN: A configured DeployPackageBuild operator
        gdr_sql_path = os.path.join(self.s3_transform_dir, "gdr.sql")

        operator = create_deploy_package_build(
            repo=self.local_connector,
            full_shard_url=full_shard_url,
            dataset_artifacts_path=os.path.join(
                "s3://", self.bucket_name, f"etl/dataset0/{revision}/artifacts/"
            ),
            deployment_config={"test": "mock.local"},
            git_meta_repo=self.metadatastore_repo,
            deployment_params=DeploymentParameters(deploy=True, activate=True),
            transform_path=transform_path,
            transform_path_override=transform_path_override,
            data_transformation_url_s3=self.s3_transform_dir,
            gdr_sql=gdr_sql_path,
            gdr_validation=gdr_sql_path,
        )

        self.s3_client.upload_file(
            Filename=str(self.path_to_resources / "data_specification.xlsx"),
            Bucket=self.bucket_name,
            Key=f"etl/dataset0/{revision}/artifacts/data_specification.xlsx",
        )

        conn_repo_root = operator.repo
        mds_repo_root = operator.git_meta_repo

        # GIVEN: The operator has been configured
        operator.populate_attributes()

        # WHEN: The operator is executed
        if operator.revision == "20220103":
            with pytest.raises(ExceptionGroup):
                operator.execute({})
        else:
            operator.execute({})

        # THEN: A new branch is created in the metadatastore repo with the correct commits and files
        repo = Repo(mds_repo_root)
        assert len(repo.heads) == 1 + 1, (
            f"Expecting metadatastore repo {mds_repo_root} to have an additional branch"
        )
        branch = [h for h in repo.heads if h.name != "master"][0]
        assert branch.name.startswith("dbc_"), (
            f"Package branch should start with 'dbc_' but was {branch.name}"
        )
        assert len(list(repo.iter_commits(branch))) == commits_number, (
            f"Expecting {commits_number} commits in the created metadatastore branch"
        )

        # THEN: The correct files are touched in the metadatastore branch
        diff = repo.heads[0].commit.diff()
        assert len(diff) == touched_files_number, (
            f"Wrong number of touched files on {branch.name}"
        )
        for d in diff:
            assert d.a_path == d.b_path, f"{d.a_path} moved to {d.b_path}"
            if os.path.basename(d.a_path) == "aetionpkg.shard.json":
                assert Path(d.a_path).parts[0] == "shardpackage", (
                    f"{d.a_path} should be under shardpackage/"
                )
            elif os.path.basename(d.a_path) == "uuidMap.csv" or os.path.basename(
                d.a_path
            ).endswith("_datasetDescription.ftl"):
                assert Path(d.a_path).parts[0] == "reportTemplates", (
                    f"{d.a_path} should be under reportTemplates/"
                )
            else:
                assert Path(d.a_path).parts[0] == "metaDataStore", (
                    f"{d.a_path} should be under metaDataStore/"
                )

        # THEN: The content of the generated package files is correct
        branch.checkout()
        with open(
            Path(
                mds_repo_root,
                [
                    d.a_path
                    for d in diff
                    if Path(d.a_path).name == "aetionpkg.shard.json"
                ][0],
            )
        ) as f:
            shard_package = json.load(f)
            for field in [
                "etlDir",
                "patientNum",
                "supportSampleData",
                "sampleDataPatientNum",
            ]:
                assert field in shard_package, (
                    f"aetionpkg.shard.json is missing '{field}'"
                )
            assert shard_package["etlDir"].endswith("/")
            assert not shard_package["etlDir"].endswith("//")
            # Assert the exact order of the json fields
            f.seek(0)
            contents = f.read()
            expected = {
                "name": "DATASET_7",
                "revision": operator.revision,
                "tag": "instance_a",
                "instanceNameOverride": f"DATASET_7_instance_a_{operator.revision}",
                "runtimeDir": f"s3://mock.local/runtime/dataset_7/instance_a/{operator.revision}/",
                "patientNum": 42,
                "sampleShardNum": 251,
                "sampleShardPct": sampleShardPct,
                "supportSampleData": True,
                "sampleDataPatientNum": 1,
                "__comment__": "rest of fields omitted",
                "shardGUID": shard_guid,
                "etlDir": f"{operator.full_shard_url}/",
            }
            assert contents == json.dumps(expected, indent=2)

        with open(
            Path(
                mds_repo_root,
                [d.a_path for d in diff if Path(d.a_path).name == SHARD_METADATA_JSON][
                    0
                ],
            )
        ) as f:
            assert f.name.endswith(
                f"metaDataStore/versioned/dataset_7/instance_a/{operator.revision}/{SHARD_METADATA_JSON}"
            )
            dataset_json = json.load(f)
            expected.pop("runtimeDir")
            expected.pop("__comment__")
            expected.pop("shardGUID")
            assert dataset_json == expected

        # THEN: The connector repository is tagged correctly
        repo = Repo(conn_repo_root)
        tags = [tag.name for tag in repo.tags if operator.revision in tag.name]
        assert len(tags) == 1, (
            f"Expecting connector repo {conn_repo_root} to have been tagged"
        )
        assert repo.tags[0].name.startswith("DBC_"), (
            f"connector tag should start with 'DBC_' but was {repo.tags[0].name}"
        )

        # THEN: The DDS endpoint is called with the correct payload
        dds_requests = [
            r
            for r in self.mock.request_history
            if r.url
            == "http://dds.invalid/v1/customers/some_client/environments/prod/ingestion"
        ]
        assert len(dds_requests) == 1
        assert_dds_test_payload(
            dds_requests[0].json(),
            name="dataset_7",
            tag="instance_a",
            revision=operator.revision,
            branch=f"dbc_some_client_dataset_7_instance_a_{operator.revision}_legacy_test",
            deploy=True,
            activate=True,
        )

    @pytest.mark.parametrize(
        "revision, transform_path, transform_path_override, failed",
        [
            (
                "20220101",
                "test_dbc",
                "transform_path_override",
                False,
            ),
            (
                "20220101",
                "test_dbc",
                "",
                True,
            ),
            ("20220101", "wrong_path", "", True),
            (
                "20220101",
                "transform_path_override",
                "",
                True,
            ),
        ],
    )
    def test_validate_if_files_are_in_master(
        self,
        revision: str,
        transform_path: str,
        transform_path_override: str,
        failed: bool,
    ):
        # Given: shard-stage artifacts in S3 like aetionpkg.shard.json
        full_shard_path = f"etl/dataset0/{revision}/full-shard"
        self.upload_files_from(
            local_path=self.path_to_resources / "s3" / revision / "full-shard",
            s3_path=full_shard_path,
        )
        full_shard_url = os.path.join("s3://", self.bucket_name, full_shard_path)

        gdr_sql_path = os.path.join(self.s3_transform_dir, "gdr.sql")

        operator = create_deploy_package_build(
            repo=self.local_connector,
            full_shard_url=full_shard_url,
            dataset_artifacts_path=os.path.join(
                "s3://", self.bucket_name, f"etl/dataset0/{revision}/artifacts/"
            ),
            deployment_config={"test": "mock.local"},
            git_meta_repo=self.metadatastore_repo,
            deployment_params=DeploymentParameters(deploy=True, activate=True),
            transform_path=transform_path,
            transform_path_override=transform_path_override,
            data_transformation_url_s3=self.s3_transform_dir,
            gdr_sql=gdr_sql_path,
            gdr_validation=gdr_sql_path,
        )

        if failed:
            with pytest.raises(AirflowException):
                operator.execute({})
        else:
            operator.execute({})
