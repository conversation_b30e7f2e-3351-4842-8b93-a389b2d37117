import collections
import json
import os
from pathlib import Path
from typing import Optional
from unittest.mock import Mock, patch

import pytest
from airflow.exceptions import AirflowException
from tools.deploy_common import (
    REPORT_DIR,
    SHARD_METADATA_JSON,
    UUID_MAP_CSV,
    DeployCommonHook,
    PackageFileMetadata,
)
from tools.git_credentials import GitCredentials

EVENTS_DISCARDED_OUTSIDE_GDR = "eventsDiscardedOutsideGDR"


# Test fixtures and helpers
@pytest.fixture
def mock_git_credentials() -> GitCredentials:
    """Create mock GitCredentials for testing."""
    return GitCredentials(
        private_key="test-private-key",
        branch="master",
        connector_repo="**************:test/connector.git",
        metadatastore_repo="**************:test/metadatastore.git",
    )


@pytest.fixture
def deploy_hook(mock_git_credentials: GitCredentials) -> DeployCommonHook:
    """Create DeployCommonHook instance for testing."""
    return DeployCommonHook(git_creds=mock_git_credentials, aws_conn_id="test_aws_conn")


@pytest.fixture
def sample_uuid_map_csv() -> str:
    """Sample UUID mapping CSV content for testing."""
    return """dataset1/tag1/revision1/template1.ftl,uuid-1234
dataset1/tag1/template2.ftl,uuid-5678
dataset1/template3.ftl,uuid-9012
dataset2/tag2/revision2/template4.ftl,uuid-3456
dataset2/template5.ftl,uuid-7890"""


@pytest.fixture
def sample_package_file_content() -> dict:
    """Sample package file JSON content for testing."""
    return {
        "name": "test-dataset",
        "version": "1.0.0",
        "runtimeDir": "s3://original-bucket/runtime/",
        "shardGUID": "original-uuid",
        "etlDir": "s3://original-etl/",
        "additionalField": "value",
    }


def create_mock_s3_file(content: str) -> Mock:
    """Helper function to create properly mocked S3 file object."""
    mock_file = Mock()
    mock_file.read.return_value = content
    mock_file.__enter__ = Mock(return_value=mock_file)
    mock_file.__exit__ = Mock(return_value=None)
    return mock_file


# Test classes organized by functionality


class TestDeployCommonHookReportTemplates:
    """Test class for report template related methods."""

    def test_get_report_exact_match(self, tmp_path: Path, sample_uuid_map_csv: str):
        # Given: A metadatastore with UUID mapping file
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text(sample_uuid_map_csv)

        # When: Searching for exact match dataset1/tag1/revision1
        result = DeployCommonHook.get_report(
            metadata_dir, "dataset1", "tag1", "revision1"
        )

        # Then: Should return the most specific match
        assert result is not None
        assert result.uuid == "uuid-1234"
        assert result.file == Path("dataset1/tag1/revision1/template1.ftl")

    def test_get_report_tag_fallback(self, tmp_path: Path, sample_uuid_map_csv: str):
        # Given: A metadatastore with UUID mapping file
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text(sample_uuid_map_csv)

        # When: Searching for non-existent revision but existing tag
        result = DeployCommonHook.get_report(
            metadata_dir, "dataset1", "tag1", "nonexistent"
        )

        # Then: Should fallback to tag-level match
        assert result is not None
        assert result.uuid == "uuid-5678"
        assert result.file == Path("dataset1/tag1/template2.ftl")

    def test_get_report_dataset_fallback(
        self, tmp_path: Path, sample_uuid_map_csv: str
    ):
        # Given: A metadatastore with UUID mapping file
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text(sample_uuid_map_csv)

        # When: Searching for non-existent tag but existing dataset
        result = DeployCommonHook.get_report(
            metadata_dir, "dataset1", "nonexistent", "nonexistent"
        )

        # Then: Should fallback to dataset-level match
        assert result is not None
        assert result.uuid == "uuid-9012"
        assert result.file == Path("dataset1/template3.ftl")

    def test_get_report_no_match(self, tmp_path: Path, sample_uuid_map_csv: str):
        # Given: A metadatastore with UUID mapping file
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text(sample_uuid_map_csv)

        # When: Searching for completely non-existent dataset
        result = DeployCommonHook.get_report(
            metadata_dir, "nonexistent", "tag", "revision"
        )

        # Then: Should return None
        assert result is None

    def test_get_report_empty_mapping_file(self, tmp_path: Path):
        # Given: A metadatastore with empty UUID mapping file
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text("")

        # When: Searching for any dataset
        result = DeployCommonHook.get_report(
            metadata_dir, "dataset1", "tag1", "revision1"
        )

        # Then: Should return None
        assert result is None

    def test_get_report_malformed_csv(self, tmp_path: Path):
        # Given: A metadatastore with malformed UUID mapping file
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text("invalid,csv,format,too,many,fields")

        # When: Searching for any dataset
        # Then: Should raise ValueError due to malformed CSV
        with pytest.raises(ValueError, match="too many values to unpack"):
            DeployCommonHook.get_report(metadata_dir, "dataset1", "tag1", "revision1")

    def test_find_report_template_success(
        self, deploy_hook: DeployCommonHook, tmp_path: Path, sample_uuid_map_csv: str
    ):
        # Given: A metadatastore with UUID mapping and template file
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text(sample_uuid_map_csv)

        # Create the actual template file
        template_dir = report_dir / "dataset1" / "tag1" / "revision1"
        template_dir.mkdir(parents=True)
        template_file = template_dir / "template1.ftl"
        template_file.write_text("Template content")

        # When: Finding report template
        result = deploy_hook.find_report_template(
            metadata_dir, "dataset1", "tag1", "revision1"
        )

        # Then: Should return the relative path to template (including reportTemplates prefix)
        assert result == Path("reportTemplates/dataset1/tag1/revision1/template1.ftl")

    def test_find_report_template_no_uuid_mapping(
        self, deploy_hook: DeployCommonHook, tmp_path: Path
    ):
        # Given: A metadatastore without UUID mapping file
        metadata_dir = tmp_path / "metadatastore"

        # When: Finding report template
        # Then: Should raise FileNotFoundError (which gets wrapped in AirflowException)
        with pytest.raises(FileNotFoundError):
            deploy_hook.find_report_template(
                metadata_dir, "dataset1", "tag1", "revision1"
            )

    def test_find_report_template_file_not_exists(
        self, deploy_hook: DeployCommonHook, tmp_path: Path, sample_uuid_map_csv: str
    ):
        # Given: A metadatastore with UUID mapping but missing template file
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text(sample_uuid_map_csv)

        # When: Finding report template (file doesn't exist)
        # Then: Should raise AirflowException with specific message
        with pytest.raises(AirflowException, match="not found in the repository"):
            deploy_hook.find_report_template(
                metadata_dir, "dataset1", "tag1", "revision1"
            )

    def test_validate_report_template_exists_success(
        self, deploy_hook: DeployCommonHook, tmp_path: Path, sample_uuid_map_csv: str
    ):
        # Given: A metadatastore with valid UUID mapping and template file
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text(sample_uuid_map_csv)

        # Create the actual template file
        template_dir = report_dir / "dataset1" / "tag1" / "revision1"
        template_dir.mkdir(parents=True)
        template_file = template_dir / "template1.ftl"
        template_file.write_text("Template content")

        # When: Validating report template exists
        # Then: Should not raise any exception
        deploy_hook.validate_report_template_exists(
            str(metadata_dir), "dataset1", "tag1", "revision1"
        )

    def test_validate_report_template_exists_failure(
        self, deploy_hook: DeployCommonHook, tmp_path: Path
    ):
        # Given: A metadatastore without UUID mapping
        metadata_dir = tmp_path / "metadatastore"

        # When: Validating report template exists
        # Then: Should raise FileNotFoundError
        with pytest.raises(FileNotFoundError):
            deploy_hook.validate_report_template_exists(
                str(metadata_dir), "dataset1", "tag1", "revision1"
            )


class TestDeployCommonHookPackageFiles:
    """Test class for package file operations."""

    @patch("tools.deploy_common.AetionS3FileSystem")
    def test_update_aetion_pkg_file_basic(
        self,
        mock_s3_fs_class: Mock,
        deploy_hook: DeployCommonHook,
        sample_package_file_content: dict,
        tmp_path: Path,
    ):
        # Given: A mock S3 filesystem and package file
        mock_fs = Mock()
        mock_s3_fs_class.return_value = mock_fs
        deploy_hook._fs = mock_fs

        # Mock file reading from S3 using context manager
        mock_file = create_mock_s3_file(json.dumps(sample_package_file_content))
        mock_fs.open.return_value = mock_file

        package_file = PackageFileMetadata(
            filename="aetionpkg.shard.json", metadatastore_path="test/path"
        )

        local_path = str(tmp_path / "output.json")

        # When: Updating package file
        result = deploy_hook.update_aetion_pkg_file(
            file_s3_path="s3://bucket/source.json",
            file_local_path=local_path,
            package_file=package_file,
            dataset_name="test_dataset",
            tag="test_tag",
            revision="test_revision",
            etl_dir="s3://etl-bucket/etl",
        )

        # Then: Should update etlDir and write file locally
        assert result["etlDir"] == "s3://etl-bucket/etl/"
        assert result["name"] == "test-dataset"  # Original content preserved
        assert os.path.exists(local_path)

        # Verify file content
        with open(local_path, "r") as f:
            written_content = json.load(f)
        assert written_content["etlDir"] == "s3://etl-bucket/etl/"

    @patch("tools.deploy_common.AetionS3FileSystem")
    def test_update_aetion_pkg_file_with_bucket_override(
        self,
        mock_s3_fs_class: Mock,
        deploy_hook: DeployCommonHook,
        sample_package_file_content: dict,
        tmp_path: Path,
    ):
        # Given: A package file with bucket override
        mock_fs = Mock()
        mock_s3_fs_class.return_value = mock_fs
        deploy_hook._fs = mock_fs

        mock_file = create_mock_s3_file(json.dumps(sample_package_file_content))
        mock_fs.open.return_value = mock_file

        package_file = PackageFileMetadata(
            filename="aetionpkg.shard.json",
            metadatastore_path="test/path",
            bucket_override="override-bucket",
        )

        local_path = str(tmp_path / "output.json")

        # When: Updating package file with bucket override
        result = deploy_hook.update_aetion_pkg_file(
            file_s3_path="s3://bucket/source.json",
            file_local_path=local_path,
            package_file=package_file,
            dataset_name="test_dataset",
            tag="test_tag",
            revision="test_revision",
            etl_dir="s3://etl-bucket/etl",
        )

        # Then: Should update runtimeDir with override bucket
        expected_runtime_dir = (
            "s3://override-bucket/runtime/test_dataset/test_tag/test_revision/"
        )
        assert result["runtimeDir"] == expected_runtime_dir
        assert result["etlDir"] == "s3://etl-bucket/etl/"

    @patch("tools.deploy_common.AetionS3FileSystem")
    def test_update_aetion_pkg_file_with_uuid_override(
        self,
        mock_s3_fs_class: Mock,
        deploy_hook: DeployCommonHook,
        sample_package_file_content: dict,
        tmp_path: Path,
    ):
        # Given: A package file with UUID override
        mock_fs = Mock()
        mock_s3_fs_class.return_value = mock_fs
        deploy_hook._fs = mock_fs

        mock_file = create_mock_s3_file(json.dumps(sample_package_file_content))
        mock_fs.open.return_value = mock_file

        package_file = PackageFileMetadata(
            filename="aetionpkg.shard.json",
            metadatastore_path="test/path",
            shard_uuid_override="override-uuid-1234",
        )

        local_path = str(tmp_path / "output.json")

        # When: Updating package file with UUID override
        result = deploy_hook.update_aetion_pkg_file(
            file_s3_path="s3://bucket/source.json",
            file_local_path=local_path,
            package_file=package_file,
            dataset_name="test_dataset",
            tag="test_tag",
            revision="test_revision",
            etl_dir="s3://etl-bucket/etl",
        )

        # Then: Should update shardGUID with override
        assert result["shardGUID"] == "override-uuid-1234"
        assert result["etlDir"] == "s3://etl-bucket/etl/"

    @patch("tools.deploy_common.AetionS3FileSystem")
    def test_update_aetion_pkg_file_with_all_overrides(
        self,
        mock_s3_fs_class: Mock,
        deploy_hook: DeployCommonHook,
        sample_package_file_content: dict,
        tmp_path: Path,
    ):
        # Given: A package file with both bucket and UUID overrides
        mock_fs = Mock()
        mock_s3_fs_class.return_value = mock_fs
        deploy_hook._fs = mock_fs

        mock_file = Mock()
        mock_file.read.return_value = json.dumps(sample_package_file_content)
        mock_fs.open.return_value.__enter__.return_value = mock_file

        package_file = PackageFileMetadata(
            filename="aetionpkg.shard.json",
            metadatastore_path="test/path",
            bucket_override="override-bucket",
            shard_uuid_override="override-uuid-1234",
        )

        local_path = str(tmp_path / "output.json")

        # When: Updating package file with all overrides
        result = deploy_hook.update_aetion_pkg_file(
            file_s3_path="s3://bucket/source.json",
            file_local_path=local_path,
            package_file=package_file,
            dataset_name="test_dataset",
            tag="test_tag",
            revision="test_revision",
            etl_dir="s3://etl-bucket/etl/",
        )

        # Then: Should apply all overrides
        expected_runtime_dir = (
            "s3://override-bucket/runtime/test_dataset/test_tag/test_revision/"
        )
        assert result["runtimeDir"] == expected_runtime_dir
        assert result["shardGUID"] == "override-uuid-1234"
        assert result["etlDir"] == "s3://etl-bucket/etl/"

    @patch("tools.deploy_common.AetionS3FileSystem")
    def test_update_aetion_pkg_file_preserves_order(
        self, mock_s3_fs_class: Mock, deploy_hook: DeployCommonHook, tmp_path: Path
    ):
        # Given: A package file with ordered content
        mock_fs = Mock()
        mock_s3_fs_class.return_value = mock_fs
        deploy_hook._fs = mock_fs

        # Use OrderedDict to test order preservation
        ordered_content = collections.OrderedDict(
            [
                ("name", "test-dataset"),
                ("version", "1.0.0"),
                ("runtimeDir", "s3://original-bucket/runtime/"),
                ("shardGUID", "original-uuid"),
                ("etlDir", "s3://original-etl/"),
                ("additionalField", "value"),
            ]
        )

        mock_file = Mock()
        mock_file.read.return_value = json.dumps(ordered_content)
        mock_fs.open.return_value.__enter__.return_value = mock_file

        package_file = PackageFileMetadata(
            filename="aetionpkg.shard.json", metadatastore_path="test/path"
        )

        local_path = str(tmp_path / "output.json")

        # When: Updating package file
        result = deploy_hook.update_aetion_pkg_file(
            file_s3_path="s3://bucket/source.json",
            file_local_path=local_path,
            package_file=package_file,
            dataset_name="test_dataset",
            tag="test_tag",
            revision="test_revision",
            etl_dir="s3://etl-bucket/etl",
        )

        # Then: Should preserve field order and update etlDir
        assert isinstance(result, collections.OrderedDict)
        assert result["etlDir"] == "s3://etl-bucket/etl/"

    @patch("tools.deploy_common.AetionS3FileSystem")
    def test_update_aetion_pkg_file_creates_directories(
        self,
        mock_s3_fs_class: Mock,
        deploy_hook: DeployCommonHook,
        sample_package_file_content: dict,
        tmp_path: Path,
    ):
        # Given: A nested local path that doesn't exist
        mock_fs = Mock()
        mock_s3_fs_class.return_value = mock_fs
        deploy_hook._fs = mock_fs

        mock_file = Mock()
        mock_file.read.return_value = json.dumps(sample_package_file_content)
        mock_fs.open.return_value.__enter__.return_value = mock_file

        package_file = PackageFileMetadata(
            filename="aetionpkg.shard.json", metadatastore_path="test/path"
        )

        nested_path = tmp_path / "deep" / "nested" / "path" / "output.json"
        local_path = str(nested_path)

        # When: Updating package file with nested path
        result = deploy_hook.update_aetion_pkg_file(
            file_s3_path="s3://bucket/source.json",
            file_local_path=local_path,
            package_file=package_file,
            dataset_name="test_dataset",
            tag="test_tag",
            revision="test_revision",
            etl_dir="s3://etl-bucket/etl",
        )

        # Then: Should create directories and write file
        assert os.path.exists(local_path)
        assert result["etlDir"] == "s3://etl-bucket/etl/"

    @patch("tools.deploy_common.AetionS3FileSystem")
    def test_update_aetion_pkg_file_handles_trailing_slashes(
        self,
        mock_s3_fs_class: Mock,
        deploy_hook: DeployCommonHook,
        sample_package_file_content: dict,
        tmp_path: Path,
    ):
        # Given: ETL directory with trailing slashes
        mock_fs = Mock()
        mock_s3_fs_class.return_value = mock_fs
        deploy_hook._fs = mock_fs

        mock_file = Mock()
        mock_file.read.return_value = json.dumps(sample_package_file_content)
        mock_fs.open.return_value.__enter__.return_value = mock_file

        package_file = PackageFileMetadata(
            filename="aetionpkg.shard.json", metadatastore_path="test/path"
        )

        local_path = str(tmp_path / "output.json")

        # When: Updating package file with ETL dir having trailing slashes
        result = deploy_hook.update_aetion_pkg_file(
            file_s3_path="s3://bucket/source.json",
            file_local_path=local_path,
            package_file=package_file,
            dataset_name="test_dataset",
            tag="test_tag",
            revision="test_revision",
            etl_dir="s3://etl-bucket/etl///",
        )

        # Then: Should normalize trailing slashes
        assert result["etlDir"] == "s3://etl-bucket/etl/"

    @patch("tools.deploy_common.AetionS3FileSystem")
    def test_update_aetion_pkg_file_s3_error(
        self, mock_s3_fs_class: Mock, deploy_hook: DeployCommonHook
    ):
        # Given: S3 filesystem that raises an error
        mock_fs = Mock()
        mock_s3_fs_class.return_value = mock_fs
        deploy_hook._fs = mock_fs

        mock_fs.open.side_effect = Exception("S3 connection error")

        package_file = PackageFileMetadata(
            filename="aetionpkg.shard.json", metadatastore_path="test/path"
        )

        # When: Updating package file with S3 error
        # Then: Should propagate the exception
        with pytest.raises(Exception, match="S3 connection error"):
            deploy_hook.update_aetion_pkg_file(
                file_s3_path="s3://bucket/source.json",
                file_local_path="/tmp/output.json",
                package_file=package_file,
                dataset_name="test_dataset",
                tag="test_tag",
                revision="test_revision",
                etl_dir="s3://etl-bucket/etl",
            )


class TestDeployCommonHookShardMetadata:
    """Test class for shard metadata operations."""

    @pytest.mark.parametrize("events_discarded_outside_gdr", [True, False, None])
    def test_create_shard_metadata_json_events_discarded_outside_gdr(
        self, events_discarded_outside_gdr: Optional[bool], tmp_path: Path
    ):
        # Given: Shard package data with events_discarded_outside_gdr
        shard_package_data = {}
        if events_discarded_outside_gdr is not None:
            shard_package_data[EVENTS_DISCARDED_OUTSIDE_GDR] = (
                events_discarded_outside_gdr
            )

        # When: create_shard_metadata_json is called
        DeployCommonHook.create_shard_metadata_json(tmp_path, shard_package_data)

        # Then: The shard_metadata.json file should be created with the eventsDiscardedOutsideGDR field
        dataset_path = tmp_path / SHARD_METADATA_JSON
        assert dataset_path.exists()
        actual_shard_metadata = json.loads(dataset_path.read_text())
        if events_discarded_outside_gdr is None:
            assert EVENTS_DISCARDED_OUTSIDE_GDR not in actual_shard_metadata
        else:
            assert EVENTS_DISCARDED_OUTSIDE_GDR in actual_shard_metadata
            assert (
                actual_shard_metadata[EVENTS_DISCARDED_OUTSIDE_GDR]
                == events_discarded_outside_gdr
            )

    def test_create_shard_metadata_json_all_fields(self, tmp_path: Path):
        # Given: Shard package data with all possible fields
        shard_package_data = {
            "name": "test_dataset",
            "tag": "test_tag",
            "revision": "test_revision",
            "shardNum": 5,
            "patientNum": 1000,
            "instanceNameOverride": "test_instance",
            "sampleShardNum": 2,
            "sampleShardPct": 10.5,
            "supportSampleData": True,
            "sampleDataPatientNum": 100,
            "dataModel": "test_model",
            "dataModelVersion": "1.0.0",
            "etlDir": "s3://etl-bucket/etl/",
            "startDate": "2024-01-01",
            "endDate": "2024-12-31",
            "createDate": "2024-06-15",
            "eventsDiscardedOutsideGDR": True,
            "extraField": "should_be_ignored",  # This should not appear in output
        }

        # When: create_shard_metadata_json is called
        DeployCommonHook.create_shard_metadata_json(tmp_path, shard_package_data)

        # Then: Should create file with only expected fields
        dataset_path = tmp_path / SHARD_METADATA_JSON
        assert dataset_path.exists()
        actual_shard_metadata = json.loads(dataset_path.read_text())

        # Check all expected fields are present
        expected_fields = [
            "name",
            "tag",
            "revision",
            "shardNum",
            "patientNum",
            "instanceNameOverride",
            "sampleShardNum",
            "sampleShardPct",
            "supportSampleData",
            "sampleDataPatientNum",
            "dataModel",
            "dataModelVersion",
            "etlDir",
            "startDate",
            "endDate",
            "createDate",
            "eventsDiscardedOutsideGDR",
        ]

        for field in expected_fields:
            assert field in actual_shard_metadata
            assert actual_shard_metadata[field] == shard_package_data[field]

        # Check that extra field is not included
        assert "extraField" not in actual_shard_metadata

    def test_create_shard_metadata_json_minimal_fields(self, tmp_path: Path):
        # Given: Shard package data with only minimal fields
        shard_package_data = {"name": "minimal_dataset", "tag": "minimal_tag"}

        # When: create_shard_metadata_json is called
        DeployCommonHook.create_shard_metadata_json(tmp_path, shard_package_data)

        # Then: Should create file with only provided fields
        dataset_path = tmp_path / SHARD_METADATA_JSON
        assert dataset_path.exists()
        actual_shard_metadata = json.loads(dataset_path.read_text())

        assert actual_shard_metadata == {
            "name": "minimal_dataset",
            "tag": "minimal_tag",
        }

    def test_create_shard_metadata_json_empty_data(self, tmp_path: Path):
        # Given: Empty shard package data
        shard_package_data = {}

        # When: create_shard_metadata_json is called
        DeployCommonHook.create_shard_metadata_json(tmp_path, shard_package_data)

        # Then: Should create empty JSON file
        dataset_path = tmp_path / SHARD_METADATA_JSON
        assert dataset_path.exists()
        actual_shard_metadata = json.loads(dataset_path.read_text())
        assert actual_shard_metadata == {}

    def test_create_shard_metadata_json_numeric_types(self, tmp_path: Path):
        # Given: Shard package data with various numeric types
        shard_package_data = {
            "shardNum": 5,
            "patientNum": 1000,
            "sampleShardNum": 2,
            "sampleShardPct": 10.5,
            "sampleDataPatientNum": 100,
        }

        # When: create_shard_metadata_json is called
        DeployCommonHook.create_shard_metadata_json(tmp_path, shard_package_data)

        # Then: Should preserve numeric types
        dataset_path = tmp_path / SHARD_METADATA_JSON
        assert dataset_path.exists()
        actual_shard_metadata = json.loads(dataset_path.read_text())

        assert isinstance(actual_shard_metadata["shardNum"], int)
        assert isinstance(actual_shard_metadata["patientNum"], int)
        assert isinstance(actual_shard_metadata["sampleShardNum"], int)
        assert isinstance(actual_shard_metadata["sampleShardPct"], float)
        assert isinstance(actual_shard_metadata["sampleDataPatientNum"], int)

    def test_create_shard_metadata_json_boolean_types(self, tmp_path: Path):
        # Given: Shard package data with boolean fields
        shard_package_data = {
            "supportSampleData": True,
            "eventsDiscardedOutsideGDR": False,
        }

        # When: create_shard_metadata_json is called
        DeployCommonHook.create_shard_metadata_json(tmp_path, shard_package_data)

        # Then: Should preserve boolean types
        dataset_path = tmp_path / SHARD_METADATA_JSON
        assert dataset_path.exists()
        actual_shard_metadata = json.loads(dataset_path.read_text())

        assert actual_shard_metadata["supportSampleData"] is True
        assert actual_shard_metadata["eventsDiscardedOutsideGDR"] is False

    def test_create_shard_metadata_json_file_formatting(self, tmp_path: Path):
        # Given: Shard package data
        shard_package_data = {
            "name": "test_dataset",
            "tag": "test_tag",
            "revision": "test_revision",
        }

        # When: create_shard_metadata_json is called
        DeployCommonHook.create_shard_metadata_json(tmp_path, shard_package_data)

        # Then: Should create properly formatted JSON file
        dataset_path = tmp_path / SHARD_METADATA_JSON
        assert dataset_path.exists()

        # Check that file is properly formatted with indentation
        file_content = dataset_path.read_text()
        assert "  " in file_content  # Should have indentation
        assert file_content.count("\n") > 1  # Should have multiple lines


class TestDeployCommonHookIntegration:
    """Test class for integration scenarios and error handling."""

    def test_hook_initialization(self, mock_git_credentials: GitCredentials):
        # Given: Valid git credentials
        # When: Creating DeployCommonHook
        hook = DeployCommonHook(git_creds=mock_git_credentials, aws_conn_id="test_conn")

        # Then: Should initialize properly
        assert hook.git_creds == mock_git_credentials
        assert hook.aws_conn_id == "test_conn"

    def test_hook_initialization_default_aws_conn(
        self, mock_git_credentials: GitCredentials
    ):
        # Given: Valid git credentials without aws_conn_id
        # When: Creating DeployCommonHook with default connection
        hook = DeployCommonHook(git_creds=mock_git_credentials)

        # Then: Should use default AWS connection
        assert hook.aws_conn_id == "aws_default"

    @patch("tools.deploy_common.AetionS3FileSystem")
    def test_fs_property_caching(
        self, mock_s3_fs_class: Mock, deploy_hook: DeployCommonHook
    ):
        # Given: A deploy hook instance
        mock_fs = Mock()
        mock_s3_fs_class.return_value = mock_fs

        # When: Accessing fs property multiple times
        fs1 = deploy_hook.fs
        fs2 = deploy_hook.fs

        # Then: Should return the same cached instance
        assert fs1 is fs2
        mock_s3_fs_class.assert_called_once_with("test_aws_conn")

    def test_get_report_with_duplicate_entries(self, tmp_path: Path):
        # Given: A metadatastore with duplicate entries in UUID mapping
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        duplicate_csv = """dataset1/tag1/revision1/template1.ftl,uuid-1234
dataset1/tag1/revision1/template1.ftl,uuid-5678
dataset1/tag1/template2.ftl,uuid-9012"""

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text(duplicate_csv)

        # When: Searching for dataset with duplicate entries
        result = DeployCommonHook.get_report(
            metadata_dir, "dataset1", "tag1", "revision1"
        )

        # Then: Should return the last matching entry (most recent in file)
        assert result is not None
        assert result.uuid == "uuid-5678"

    def test_get_report_with_special_characters(self, tmp_path: Path):
        # Given: A metadatastore with special characters in paths
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        special_csv = """dataset-with-dashes/tag_with_underscores/revision.with.dots/template-file.ftl,uuid-special"""

        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text(special_csv)

        # When: Searching for dataset with special characters
        result = DeployCommonHook.get_report(
            metadata_dir,
            "dataset-with-dashes",
            "tag_with_underscores",
            "revision.with.dots",
        )

        # Then: Should handle special characters correctly
        assert result is not None
        assert result.uuid == "uuid-special"

    def test_get_report_missing_uuid_map_file(self, tmp_path: Path):
        # Given: A metadatastore without UUID mapping file
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        # When: Searching for any dataset
        # Then: Should raise FileNotFoundError
        with pytest.raises(FileNotFoundError):
            DeployCommonHook.get_report(metadata_dir, "dataset1", "tag1", "revision1")

    def test_get_report_missing_report_dir(self, tmp_path: Path):
        # Given: A metadatastore without report directory
        metadata_dir = tmp_path / "metadatastore"
        metadata_dir.mkdir()

        # When: Searching for any dataset
        # Then: Should raise FileNotFoundError
        with pytest.raises(FileNotFoundError):
            DeployCommonHook.get_report(metadata_dir, "dataset1", "tag1", "revision1")

    @patch("tools.deploy_common.AetionS3FileSystem")
    def test_update_aetion_pkg_file_invalid_json(
        self, mock_s3_fs_class: Mock, deploy_hook: DeployCommonHook
    ):
        # Given: S3 file with invalid JSON content
        mock_fs = Mock()
        mock_s3_fs_class.return_value = mock_fs
        deploy_hook._fs = mock_fs

        mock_file = Mock()
        mock_file.read.return_value = "invalid json content"
        mock_fs.open.return_value.__enter__.return_value = mock_file

        package_file = PackageFileMetadata(
            filename="aetionpkg.shard.json", metadatastore_path="test/path"
        )

        # When: Updating package file with invalid JSON
        # Then: Should raise JSON decode error
        with pytest.raises(json.JSONDecodeError):
            deploy_hook.update_aetion_pkg_file(
                file_s3_path="s3://bucket/source.json",
                file_local_path="/tmp/output.json",
                package_file=package_file,
                dataset_name="test_dataset",
                tag="test_tag",
                revision="test_revision",
                etl_dir="s3://etl-bucket/etl",
            )

    def test_create_shard_metadata_json_permission_error(self, tmp_path: Path):
        # Given: A directory without write permissions
        readonly_dir = tmp_path / "readonly"
        readonly_dir.mkdir()
        readonly_dir.chmod(0o444)  # Read-only permissions

        shard_package_data = {"name": "test"}

        try:
            # When: Attempting to create shard metadata in readonly directory
            # Then: Should raise PermissionError
            with pytest.raises(PermissionError):
                DeployCommonHook.create_shard_metadata_json(
                    readonly_dir, shard_package_data
                )
        finally:
            # Cleanup: Restore permissions for cleanup
            readonly_dir.chmod(0o755)

    def test_end_to_end_workflow_simulation(
        self,
        deploy_hook: DeployCommonHook,
        tmp_path: Path,
        sample_uuid_map_csv: str,
        sample_package_file_content: dict,
    ):
        # Given: A complete metadatastore setup
        metadata_dir = tmp_path / "metadatastore"
        report_dir = metadata_dir / REPORT_DIR.rstrip("/")
        report_dir.mkdir(parents=True)

        # Setup UUID mapping
        uuid_map_file = report_dir / UUID_MAP_CSV
        uuid_map_file.write_text(sample_uuid_map_csv)

        # Create template file
        template_dir = report_dir / "dataset1" / "tag1" / "revision1"
        template_dir.mkdir(parents=True)
        template_file = template_dir / "template1.ftl"
        template_file.write_text("Template content")

        # When: Running through validation workflow
        # Then: Should complete without errors
        deploy_hook.validate_report_template_exists(
            str(metadata_dir), "dataset1", "tag1", "revision1"
        )

        # And: Should find correct report template
        report = DeployCommonHook.get_report(
            metadata_dir, "dataset1", "tag1", "revision1"
        )
        assert report is not None
        assert report.uuid == "uuid-1234"

        # And: Should create shard metadata successfully
        shard_data = {"name": "dataset1", "tag": "tag1", "revision": "revision1"}
        DeployCommonHook.create_shard_metadata_json(tmp_path, shard_data)

        shard_file = tmp_path / SHARD_METADATA_JSON
        assert shard_file.exists()
        shard_content = json.loads(shard_file.read_text())
        assert shard_content["name"] == "dataset1"