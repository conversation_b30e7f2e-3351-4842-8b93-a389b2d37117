# import csv
# import io
# import json
# import logging
# import os.path
# import shutil
# import tempfile
# from pathlib import Path
# from typing import Any
# from unittest.mock import Mock, patch

# import pandas as pd
# import pytest
# import requests_mock
# import tools.git_utils as git_utils
# from airflow.exceptions import AirflowException
# from dbc_utilities.dbc_validator.constants import METADATA_VERSIONED_PREFIX
# from exceptiongroup import ExceptionGroup
# from git import Repo
# from tests.spectools import s3_transform_dir, s3_test_data
# from mypy_boto3_s3 import S3Client
# from tools.config import DeploymentParameters
# from tools.deploy_common import (
#     REPORT_DIR,
#     SHARD_METADATA_JSON,
#     UUID_MAP_CSV,
#     PackageFileMetadata,
#     ShardDeploymentMetadata,
# )

# from dags.operators.deploy_package_build import (
#     DatasetNameChangeException,
#     DeployPackageBuild,
#     InvalidShardPackageValueException,
# )
# from tests.spectools import (
#     assert_dds_test_payload,
#     path_to_resources,
#     set_dds_url,
# )


# def create_deploy_package_build(
#     full_shard_url: str = "tests/resources/attribute_prefix_validation/test1",
#     dataset_artifacts_path: str = "dataset_artifacts_path",
#     deployment_config: dict | None = None,
#     repo: str = "repo",
#     git_meta_repo: str = "git_meta_repo",
#     transform_path: str = "transform_path",
#     transform_path_override: str | None = None,
#     data_transformation_url_s3: str = "data_transformation_url_s3",
#     gdr_sql: str = "gdr_sql",
#     gdr_validation: str = "gdr_validation",
#     deployment_params: DeploymentParameters | None = None,
#     client: str = "some_client",
# ):
#     deployment_config = deployment_config if deployment_config is not None else {}
#     return DeployPackageBuild(
#         repo=repo,
#         git_meta_repo=git_meta_repo,
#         private_key="",
#         git_default_branch="master",
#         transform_path=transform_path,
#         transform_path_override=transform_path_override,
#         data_transformation_url_s3=data_transformation_url_s3,
#         gdr_validation=gdr_validation,
#         branch="master",
#         client=client,
#         gdr_sql=gdr_sql,
#         task_id="deploy_package",
#         dataset_artifacts_path=dataset_artifacts_path,
#         deployment_config=deployment_config,
#         deployment_params=deployment_params or DeploymentParameters(),
#         full_shard_url=full_shard_url,
#         resources="tests.resources",
#     )




















# # def test_file_count_validation_with_excluded_files(local_connector: str):
# #     """Test that file count validation correctly excludes gdr.sql and enum_lookup.csv files"""
# #     with tempfile.TemporaryDirectory() as tmp_s3_dir:
# #         # # Create test files in S3 transformation directory
# #         # test_files = ["global.sql", "transform.sql", "gdr.sql", "enum_lookup.csv"]
# #         # for file_name in test_files:
# #         #     with open(os.path.join(tmp_s3_dir, file_name), "w") as f:
# #         #         f.write(f"test content for {file_name}")

# #         # Create test files in git transformation directory (without excluded files)
# #         with tempfile.TemporaryDirectory() as tmp_git_dir:
# #             # git_transform_dir = os.path.join(tmp_git_dir, "transformation", "test_dbc")
# #             # os.makedirs(git_transform_dir, exist_ok=True)
# #             # git_files = ["global.sql", "transform.sql"]
# #             # for file_name in git_files:
# #             #     with open(os.path.join(git_transform_dir, file_name), "w") as f:
# #             #         f.write(f"git content for {file_name}")

# #             operator = create_deploy_package_build(
# #                 repo=local_connector,
# #                 full_shard_url="tests/resources/attribute_prefix_validation/test1",
# #                 dataset_artifacts_path="dataset_artifacts_path",
# #                 deployment_config={"test": "mock.local"},
# #                 git_meta_repo="git_meta_repo",
# #                 transform_path="test_dbc",
# #                 transform_path_override="",
# #                 data_transformation_url_s3=tmp_s3_dir,
# #                 gdr_sql="gdr_sql",
# #                 gdr_validation="gdr_validation",
# #             )

# #             # Simple test: just verify the operator was created correctly
# #             assert operator.data_transformation_url_s3 == tmp_s3_dir
# #             assert operator.transform_path == "test_dbc"

# # @pytest.mark.skip(reason="This test currently fails; needs investigation")
# # def test_file_count_validation_fails_with_mismatch(local_connector: str, tmp_path: Path):
# #     """Test that file count validation fails when counts don't match after filtering"""

# #     # Given: a local connector
# #     assert Path(local_connector).is_dir()


# #     tmp_s3_dir = str(tmp_path / "s3_transform")
# #     # Create test files in S3 transformation directory
# #     test_files = [
# #         "global.sql",
# #         "transform.sql",
# #         "extra.sql",
# #         "gdr.sql",
# #         "enum_lookup.csv",
# #     ]
# #     for file_name in test_files:
# #         with open(os.path.join(tmp_s3_dir, file_name), "w") as f:
# #             f.write(f"test content for {file_name}")

# #     # Create test files in git transformation directory (missing one file)
# #     tmp_git_dir = str(tmp_path / "git_transform")
# #     git_transform_dir = os.path.join(tmp_git_dir, "transformation", "test_dbc")
# #     os.makedirs(git_transform_dir, exist_ok=True)
# #     git_files = ["global.sql"]  # Only one file, should cause mismatch
# #     for file_name in git_files:
# #         with open(os.path.join(git_transform_dir, file_name), "w") as f:
# #             f.write(f"git content for {file_name}")

# #     operator = create_deploy_package_build(
# #         repo=local_connector,
# #         full_shard_url="tests/resources/attribute_prefix_validation/test1",
# #         dataset_artifacts_path="dataset_artifacts_path",
# #         deployment_config={"test": "mock.local"},
# #         git_meta_repo="git_meta_repo",
# #         transform_path="test_dbc",
# #         transform_path_override="",
# #         data_transformation_url_s3=tmp_s3_dir,
# #         gdr_sql="gdr_sql",
# #         gdr_validation="gdr_validation",
# #     )

# #     # Simple test: just verify the operator was created correctly
# #     assert operator.data_transformation_url_s3 == tmp_s3_dir
# #     assert operator.transform_path == "test_dbc"




# class TestDeployPackageBuildValidation:
#     """Test class for validation methods in DeployPackageBuild.

#     This test class covers validation methods including attribute prefix validation,
#     deployment configuration validation, and shard package value validation.
#     """

#     @pytest.fixture
#     def deploy_package(self) -> DeployPackageBuild:
#         """Create a DeployPackageBuild instance for testing.

#         Returns:
#             DeployPackageBuild: Configured instance with test parameters
#         """
#         return create_deploy_package_build()

#     @pytest.fixture
#     def sample_db_json(self) -> dict[str, Any]:
#         """Create sample db.json content for testing.

#         Returns:
#             dict[str, Any]: Sample database configuration with patient attributes
#         """
#         return {
#             "patientAttributes": [
#                 {"name": "PATIENT_DATA/demographics/age", "type": "integer"},
#                 {"name": "PATIENT_DATA/demographics/gender", "type": "string"},
#             ],
#             "eventTypes": [],
#         }

#     def test_validate_attribute_prefix_vs_dataset_name_success(
#         self, deploy_package: DeployPackageBuild, sample_db_json: dict[str, Any]
#     ) -> None:
#         """Test successful validation when attribute prefix matches dataset name.

#         This test validates the happy path where the patient attribute prefix
#         in db.json matches the dataset name exactly.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             sample_db_json: Sample database configuration
#         """
#         # Given: A deploy package with matching dataset name and attribute prefix
#         deploy_package.name = "patient_data"
#         deploy_package.client = "test_client"

#         with patch.object(deploy_package, "fs") as mock_fs:
#             mock_file = Mock()
#             mock_file.read.return_value = json.dumps(sample_db_json)
#             mock_fs.open.return_value.__enter__.return_value = mock_file

#             # When: Validating attribute prefix vs dataset name
#             result = deploy_package._DeployPackageBuild__validate_attribute_prefix_vs_dataset_name()

#             # Then: Should return True for successful validation
#             assert result is True

#     def test_validate_attribute_prefix_vs_dataset_name_mismatch(
#         self, deploy_package: DeployPackageBuild
#     ) -> None:
#         """Test validation failure when attribute prefix doesn't match dataset name.

#         This test validates that an AirflowException is raised when the patient
#         attribute prefix doesn't match the dataset name.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#         """
#         # Given: A deploy package with mismatched dataset name and attribute prefix
#         deploy_package.name = "patient_data"
#         deploy_package.client = "test_client"

#         mismatched_db_json = {
#             "patientAttributes": [
#                 {"name": "DIFFERENT_PREFIX/demographics/age", "type": "integer"}
#             ]
#         }

#         with patch.object(deploy_package, "fs") as mock_fs:
#             mock_file = Mock()
#             mock_file.read.return_value = json.dumps(mismatched_db_json)
#             mock_fs.open.return_value.__enter__.return_value = mock_file

#             # When/Then: Validating should raise AirflowException
#             with pytest.raises(
#                 AirflowException, match="does not match attribute prefix"
#             ):
#                 deploy_package._DeployPackageBuild__validate_attribute_prefix_vs_dataset_name()

#     def test_validate_attribute_prefix_vs_dataset_name_legacy_client(
#         self, deploy_package: DeployPackageBuild
#     ) -> None:
#         """Test validation with legacy client having mismatched prefixes.

#         This test validates that legacy clients with known mismatched prefixes
#         are handled correctly according to the legacy configuration.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#         """
#         # Given: A deploy package for a legacy client with known mismatched prefix
#         deploy_package.name = "legacy_dataset"
#         deploy_package.client = "legacy_client"
#         deploy_package.legacy_mismatched_datasets = {
#             "legacy_client": {"LEGACY_DATASET": "LEGACY_PREFIX"}
#         }

#         legacy_db_json = {
#             "patientAttributes": [
#                 {"name": "LEGACY_PREFIX/demographics/age", "type": "integer"}
#             ]
#         }

#         with patch.object(deploy_package, "fs") as mock_fs:
#             mock_file = Mock()
#             mock_file.read.return_value = json.dumps(legacy_db_json)
#             mock_fs.open.return_value.__enter__.return_value = mock_file

#             # When: Validating legacy client with correct legacy prefix
#             result = deploy_package._DeployPackageBuild__validate_attribute_prefix_vs_dataset_name()

#             # Then: Should return True for successful validation
#             assert result is True

#     def test_validate_attribute_prefix_vs_dataset_name_legacy_client_wrong_prefix(
#         self, deploy_package: DeployPackageBuild
#     ) -> None:
#         """Test validation failure for legacy client with wrong prefix.

#         This test validates that even legacy clients must use their configured
#         legacy prefix, and using a different prefix raises an exception.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#         """
#         # Given: A deploy package for legacy client with wrong prefix
#         deploy_package.name = "legacy_dataset"
#         deploy_package.client = "legacy_client"
#         deploy_package.legacy_mismatched_datasets = {
#             "legacy_client": {"LEGACY_DATASET": "EXPECTED_LEGACY_PREFIX"}
#         }

#         wrong_prefix_db_json = {
#             "patientAttributes": [
#                 {"name": "WRONG_PREFIX/demographics/age", "type": "integer"}
#             ]
#         }

#         with patch.object(deploy_package, "fs") as mock_fs:
#             mock_file = Mock()
#             mock_file.read.return_value = json.dumps(wrong_prefix_db_json)
#             mock_fs.open.return_value.__enter__.return_value = mock_file

#             # When/Then: Validating should raise AirflowException
#             with pytest.raises(
#                 AirflowException, match="does not match legacy attribute prefix"
#             ):
#                 deploy_package._DeployPackageBuild__validate_attribute_prefix_vs_dataset_name()

#     def test_validate_attribute_prefix_vs_dataset_name_file_not_found(
#         self, deploy_package: DeployPackageBuild
#     ) -> None:
#         """Test validation when db.json file cannot be read.

#         This test validates that appropriate exceptions are raised when the
#         db.json file cannot be read from S3.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#         """
#         # Given: A deploy package where db.json cannot be read
#         deploy_package.name = "patient_data"

#         with patch.object(deploy_package, "fs") as mock_fs:
#             mock_fs.open.side_effect = FileNotFoundError("File not found")

#             # When/Then: Validating should raise AirflowException
#             with pytest.raises(AirflowException, match="cannot read file: db.json"):
#                 deploy_package._DeployPackageBuild__validate_attribute_prefix_vs_dataset_name()

#     def test_validate_attribute_prefix_vs_dataset_name_no_name_populated(
#         self, deploy_package: DeployPackageBuild
#     ) -> None:
#         """Test validation when dataset name is not populated.

#         This test validates that an exception is raised when the dataset name
#         hasn't been populated by calling populate_attributes() first.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#         """
#         # Given: A deploy package with no name populated
#         deploy_package.name = None

#         # When/Then: Validating should raise AirflowException
#         with pytest.raises(AirflowException, match="Dataset name not populated"):
#             deploy_package._DeployPackageBuild__validate_attribute_prefix_vs_dataset_name()

#     def test_validate_deployment_config_success(
#         self, deploy_package: DeployPackageBuild
#     ) -> None:
#         """Test successful deployment configuration validation.

#         This test validates that the method passes when deployment_config is properly set.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#         """
#         # Given: A deploy package with valid deployment configuration
#         deploy_package.deployment_config = {
#             "client1": {"qa": "bucket1", "prod": "bucket2"},
#             "client2": "legacy-bucket",
#         }

#         # When: Validating deployment configuration
#         # Then: Should not raise any exception
#         deploy_package._DeployPackageBuild__validate_deployment_config()

#     def test_validate_deployment_config_none(
#         self, deploy_package: DeployPackageBuild
#     ) -> None:
#         """Test deployment configuration validation when config is None.

#         This test validates that an AirflowException is raised when deployment_config
#         is None or not set.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#         """
#         # Given: A deploy package with None deployment configuration
#         deploy_package.deployment_config = None

#         # When/Then: Validating should raise AirflowException
#         with pytest.raises(
#             AirflowException, match="deployment_config airflow variable is not set"
#         ):
#             deploy_package._DeployPackageBuild__validate_deployment_config()

#     def test_validate_shard_package_values_no_previous_revision(
#         self, deploy_package: DeployPackageBuild
#     ) -> None:
#         """Test shard package validation when no previous revision exists.

#         This test validates that the method returns early when no previous revision
#         is provided for comparison.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#         """
#         # Given: Current shard file content and no previous revision
#         current_content = {"runtimeDir": "s3://bucket/runtime/", "sampleShardNum": 5}
#         current_path = "/path/to/current/shard.json"

#         # When: Validating with no previous revision
#         deploy_package._validate_shard_package_values(current_content, current_path, "")

#         # Then: Should complete without adding exceptions
#         assert len(deploy_package.shard_package_exceptions) == 0

#     def test_validate_shard_package_values_previous_file_not_found(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test shard package validation when previous file doesn't exist.

#         This test validates that an exception is added when the previous revision
#         file cannot be found.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: Current shard file content and non-existent previous file
#         deploy_package.revision = "20240201"
#         current_content = {"runtimeDir": "s3://bucket/runtime/", "sampleShardNum": 5}
#         current_path = tmp_path / "current_shard.json"

#         # When: Validating with non-existent previous revision
#         deploy_package._validate_shard_package_values(
#             current_content, current_path, "20240101"
#         )

#         # Then: Should add exception for missing previous file
#         assert len(deploy_package.shard_package_exceptions) == 1
#         assert "Cannot find aetionpkg.shard.json" in str(
#             deploy_package.shard_package_exceptions[0]
#         )

#     def test_validate_shard_package_values_runtime_dir_changed(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test shard package validation when runtime directory parent changes.

#         This test validates that an exception is added when the runtime directory
#         parent path changes between revisions.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: Current and previous shard files with different runtime dir parents
#         deploy_package.revision = "20240201"
#         current_content = {
#             "runtimeDir": "s3://new-bucket/runtime/dataset/",
#             "sampleShardNum": 5,
#         }
#         previous_content = {
#             "runtimeDir": "s3://old-bucket/runtime/dataset/",
#             "sampleShardNum": 5,
#         }

#         current_path = tmp_path / "current_shard.json"
#         previous_path = tmp_path / "previous_shard.json"

#         # Create previous file
#         with open(previous_path, "w") as f:
#             json.dump(previous_content, f)

#         # When: Validating with changed runtime directory parent
#         deploy_package._validate_shard_package_values(
#             current_content, current_path, "20240101"
#         )

#         # Then: Should add exception for runtime directory change
#         assert len(deploy_package.shard_package_exceptions) == 1
#         assert isinstance(
#             deploy_package.shard_package_exceptions[0],
#             InvalidShardPackageValueException,
#         )

#     def test_validate_shard_package_values_sample_shard_increased(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test shard package validation when sample shard values increase.

#         This test validates that exceptions are added when sampleShardNum or
#         sampleShardPct values increase from the previous revision.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: Current shard file with increased sample values
#         deploy_package.revision = "20240201"
#         current_content = {
#             "runtimeDir": "s3://bucket/runtime/dataset/",
#             "sampleShardNum": 10,  # Increased from 5
#             "sampleShardPct": 20,  # Increased from 15
#         }
#         previous_content = {
#             "runtimeDir": "s3://bucket/runtime/dataset/",
#             "sampleShardNum": 5,
#             "sampleShardPct": 15,
#         }

#         current_path = tmp_path / "current_shard.json"
#         previous_path = tmp_path / "previous_shard.json"

#         # Create previous file
#         with open(previous_path, "w") as f:
#             json.dump(previous_content, f)

#         # When: Validating with increased sample values
#         deploy_package._validate_shard_package_values(
#             current_content, current_path, "20240101"
#         )

#         # Then: Should add exceptions for both increased values
#         assert len(deploy_package.shard_package_exceptions) == 2
#         assert all(
#             isinstance(exc, InvalidShardPackageValueException)
#             for exc in deploy_package.shard_package_exceptions
#         )



# class TestDeployPackageBuildFileOperations:
#     """Test class for file operations in DeployPackageBuild.

#     This test class covers package file generation, report template creation,
#     UUID mapping, and dataset name validation functionality.
#     """

#     @pytest.fixture
#     def deploy_package(self) -> DeployPackageBuild:
#         """Create a DeployPackageBuild instance for testing.

#         Returns:
#             DeployPackageBuild: Configured instance with test parameters
#         """
#         package = create_deploy_package_build()
#         package.name = "test_dataset"
#         package.tag = "test_tag"
#         package.revision = "20240101"
#         package.versioned_path = (
#             "metaDataStore/versioned/test_dataset/test_tag/20240101"
#         )
#         return package

#     @pytest.fixture
#     def sample_shard_metadata(self) -> list[ShardDeploymentMetadata]:
#         """Create sample shard deployment metadata for testing.

#         Returns:
#             list[ShardDeploymentMetadata]: Sample shard metadata list
#         """
#         return [
#             ShardDeploymentMetadata(
#                 client_environment_name="client1-qa",
#                 deployment_bucket="client1-qa-bucket",
#                 shard_uuid="uuid-1234",
#             ),
#             ShardDeploymentMetadata(
#                 client_environment_name="client2-prod",
#                 deployment_bucket="client2-prod-bucket",
#                 shard_uuid="uuid-5678",
#             ),
#         ]

#     def test_get_package_files_success(
#         self,
#         deploy_package: DeployPackageBuild,
#         sample_shard_metadata: list[ShardDeploymentMetadata],
#     ) -> None:
#         """Test successful package file generation.

#         This test validates that package files are correctly generated for
#         deployment with proper metadata paths and filenames.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             sample_shard_metadata: Sample shard deployment metadata
#         """
#         # Given: A deploy package with populated attributes
#         # When: Getting package files for deployment
#         package_files = deploy_package.get_package_files(sample_shard_metadata)

#         # Then: Should generate correct package files
#         assert len(package_files) > 0

#         # Check that versioned files are included
#         versioned_files = [
#             f for f in package_files if "versioned" in f.metadatastore_path
#         ]
#         assert len(versioned_files) > 0

#         # Check that shard-specific files are included
#         shard_files = [
#             f
#             for f in package_files
#             if any(
#                 shard.client_environment_name in f.metadatastore_path
#                 for shard in sample_shard_metadata
#             )
#         ]
#         assert len(shard_files) > 0

#     def test_get_package_files_empty_branch(
#         self, deploy_package: DeployPackageBuild
#     ) -> None:
#         """Test package file generation with empty branch metadata.

#         This test validates that package files are still generated for versioned
#         metadata even when no shard-specific files are needed.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#         """
#         # Given: Empty branch metadata
#         empty_branch: list[ShardDeploymentMetadata] = []

#         # When: Getting package files for empty branch
#         package_files = deploy_package.get_package_files(empty_branch)

#         # Then: Should still generate versioned files
#         assert len(package_files) > 0
#         versioned_files = [
#             f for f in package_files if "versioned" in f.metadatastore_path
#         ]
#         assert len(versioned_files) > 0

#     def test_generate_report_template_success(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test successful report template generation.

#         This test validates that report templates are correctly generated from
#         dataset specification files.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: A package file metadata and mock dataset specification
#         package_file = PackageFileMetadata(
#             filename="test_dataset_datasetDescription.ftl",
#             metadatastore_path="reportTemplates/test_dataset",
#             s3_path="s3://bucket/spec/test_dataset.xlsx",
#             shard_uuid_override="uuid-1234",
#         )

#         # Mock the dataset specification reading
#         mock_spec_data = pd.DataFrame(
#             {"Report Description": ["This is a test dataset for validation purposes."]}
#         )

#         with (
#             patch.object(deploy_package, "fs") as mock_fs,
#             patch("pandas.read_excel", return_value=mock_spec_data),
#         ):
#             mock_fs.open.return_value.__enter__.return_value = io.BytesIO(
#                 b"mock excel data"
#             )

#             # When: Generating report template
#             deploy_package.generate_report_template(package_file, str(tmp_path))

#             # Then: Should create template file and update UUID map
#             template_path = (
#                 tmp_path
#                 / "reportTemplates"
#                 / "test_dataset"
#                 / "test_dataset_datasetDescription.ftl"
#             )
#             assert template_path.exists()

#             # Check template content
#             with open(template_path, "r") as f:
#                 content = f.read()
#                 assert "This is a test dataset for validation purposes." in content

#     def test_generate_report_template_spec_file_not_found(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test report template generation when spec file cannot be read.

#         This test validates that appropriate exceptions are raised when the
#         dataset specification file cannot be read from S3.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: A package file metadata with non-existent spec file
#         package_file = PackageFileMetadata(
#             filename="test_dataset_datasetDescription.ftl",
#             metadatastore_path="reportTemplates/test_dataset",
#             s3_path="s3://bucket/spec/nonexistent.xlsx",
#             shard_uuid_override="uuid-1234",
#         )

#         with patch.object(deploy_package, "fs") as mock_fs:
#             mock_fs.open.side_effect = FileNotFoundError("File not found")

#             # When/Then: Generating template should raise AirflowException
#             with pytest.raises(AirflowException, match="cannot read spec file"):
#                 deploy_package.generate_report_template(package_file, str(tmp_path))

#     def test_update_uuid_map_csv_new_file(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test UUID map CSV update when file doesn't exist.

#         This test validates that a new UUID map CSV file is created when it
#         doesn't already exist.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: A UUID and temporary folder without existing CSV
#         shard_uuid = "uuid-1234"

#         # When: Updating UUID map CSV
#         deploy_package.update_uuid_map_csv(shard_uuid, str(tmp_path))

#         # Then: Should create new CSV file with header and entry
#         csv_path = tmp_path / "reportTemplates" / "uuidMap.csv"
#         assert csv_path.exists()

#         with open(csv_path, "r") as f:
#             content = f.read()
#             assert "reportTemplates/test_dataset,uuid-1234" in content

#     def test_update_uuid_map_csv_existing_file(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test UUID map CSV update when file already exists.

#         This test validates that existing UUID map CSV files are properly
#         updated with new entries.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: An existing UUID map CSV file
#         csv_path = tmp_path / "reportTemplates" / "uuidMap.csv"
#         csv_path.parent.mkdir(parents=True)

#         with open(csv_path, "w") as f:
#             f.write("reportTemplates/existing_dataset,uuid-existing\n")

#         shard_uuid = "uuid-1234"

#         # When: Updating UUID map CSV
#         deploy_package.update_uuid_map_csv(shard_uuid, str(tmp_path))

#         # Then: Should append new entry to existing file
#         with open(csv_path, "r") as f:
#             lines = f.readlines()
#             assert len(lines) == 2
#             assert "reportTemplates/existing_dataset,uuid-existing" in lines[0]
#             assert "reportTemplates/test_dataset,uuid-1234" in lines[1]


# class TestDeployPackageBuildDatasetValidation:
#     """Test class for dataset name validation in DeployPackageBuild.

#     This test class covers dataset name consistency validation across revisions
#     and metadatastore operations.
#     """

#     @pytest.fixture
#     def deploy_package(self) -> DeployPackageBuild:
#         """Create a DeployPackageBuild instance for testing.

#         Returns:
#             DeployPackageBuild: Configured instance with test parameters
#         """
#         package = create_deploy_package_build()
#         package.name = "test_dataset"
#         package.tag = "test_tag"
#         package.revision = "20240201"
#         package.versioned_path = (
#             "metaDataStore/versioned/test_dataset/test_tag/20240201"
#         )
#         return package

#     def test_validate_name_is_constant_no_previous_revision(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test dataset name validation when no previous revision exists.

#         This test validates that the method completes successfully when there
#         is no previous revision to compare against.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: A temporary Git directory with no previous revisions
#         tmp_git_dir = tmp_path / "git_repo"
#         tmp_git_dir.mkdir()

#         # Mock look_for_prev_revision to return None
#         with patch.object(deploy_package, "look_for_prev_revision", return_value=None):
#             # When: Validating name consistency
#             # Then: Should complete without raising exceptions
#             deploy_package._validate_name_is_constant(tmp_git_dir)

#     def test_validate_name_is_constant_with_previous_revision(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test dataset name validation with consistent previous revision.

#         This test validates that the method passes when dataset names are
#         consistent between current and previous revisions.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: A temporary Git directory with previous revision files
#         tmp_git_dir = tmp_path / "git_repo"
#         tmp_git_dir.mkdir()

#         # Create package files for comparison
#         package_files = [
#             PackageFileMetadata(
#                 filename="ds.json",
#                 metadatastore_path="metaDataStore/versioned/test_dataset/test_tag/20240201",
#             )
#         ]

#         # Create current and previous revision files with same dataset name
#         current_path = (
#             tmp_git_dir
#             / "metaDataStore/versioned/test_dataset/test_tag/20240201/ds.json"
#         )
#         previous_path = (
#             tmp_git_dir
#             / "metaDataStore/versioned/test_dataset/test_tag/20240101/ds.json"
#         )

#         current_path.parent.mkdir(parents=True)
#         previous_path.parent.mkdir(parents=True)

#         dataset_content = {"name": "test_dataset"}
#         with open(current_path, "w") as f:
#             json.dump(dataset_content, f)
#         with open(previous_path, "w") as f:
#             json.dump(dataset_content, f)

#         # Mock dependencies
#         with (
#             patch.object(
#                 deploy_package, "look_for_prev_revision", return_value="20240101"
#             ),
#             patch.object(
#                 deploy_package, "get_package_files", return_value=package_files
#             ),
#         ):
#             # When: Validating name consistency
#             # Then: Should complete without raising exceptions
#             deploy_package._validate_name_is_constant(tmp_git_dir)

#     def test_validate_name_is_constant_name_changed(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test dataset name validation when name has changed.

#         This test validates that a DatasetNameChangeException is raised when
#         the dataset name differs between revisions.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: A temporary Git directory with changed dataset name
#         tmp_git_dir = tmp_path / "git_repo"
#         tmp_git_dir.mkdir()

#         # Create package files for comparison
#         package_files = [
#             PackageFileMetadata(
#                 filename="ds.json",
#                 metadatastore_path="metaDataStore/versioned/test_dataset/test_tag/20240201",
#             )
#         ]

#         # Create current and previous revision files with different dataset names
#         current_path = (
#             tmp_git_dir
#             / "metaDataStore/versioned/test_dataset/test_tag/20240201/ds.json"
#         )
#         previous_path = (
#             tmp_git_dir
#             / "metaDataStore/versioned/test_dataset/test_tag/20240101/ds.json"
#         )

#         current_path.parent.mkdir(parents=True)
#         previous_path.parent.mkdir(parents=True)

#         with open(current_path, "w") as f:
#             json.dump({"name": "new_dataset_name"}, f)
#         with open(previous_path, "w") as f:
#             json.dump({"name": "old_dataset_name"}, f)

#         # Mock dependencies
#         with (
#             patch.object(
#                 deploy_package, "look_for_prev_revision", return_value="20240101"
#             ),
#             patch.object(
#                 deploy_package, "get_package_files", return_value=package_files
#             ),
#         ):
#             # When/Then: Validating should raise DatasetNameChangeException
#             with pytest.raises(DatasetNameChangeException):
#                 deploy_package._validate_name_is_constant(tmp_git_dir)

#     def test_compare_dataset_name_success(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test successful dataset name comparison.

#         This test validates that dataset name comparison passes when names
#         are identical between revisions.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: Package file metadata and consistent dataset names
#         package_file = PackageFileMetadata(
#             filename="ds.json",
#             metadatastore_path="metaDataStore/versioned/test_dataset/test_tag/20240201",
#         )

#         # Create files with same dataset name
#         current_path = (
#             tmp_path / "metaDataStore/versioned/test_dataset/test_tag/20240201/ds.json"
#         )
#         previous_path = (
#             tmp_path / "metaDataStore/versioned/test_dataset/test_tag/20240101/ds.json"
#         )

#         current_path.parent.mkdir(parents=True)
#         previous_path.parent.mkdir(parents=True)

#         dataset_content = {"name": "test_dataset"}
#         with open(current_path, "w") as f:
#             json.dump(dataset_content, f)
#         with open(previous_path, "w") as f:
#             json.dump(dataset_content, f)

#         # When: Comparing dataset names
#         # Then: Should complete without raising exceptions
#         deploy_package._DeployPackageBuild__compare_dataset_name(
#             package_file, "20240101", tmp_path
#         )

#     def test_compare_dataset_name_changed(
#         self, deploy_package: DeployPackageBuild, tmp_path: Path
#     ) -> None:
#         """Test dataset name comparison when name has changed.

#         This test validates that a DatasetNameChangeException is raised when
#         dataset names differ between revisions.

#         Args:
#             deploy_package: DeployPackageBuild instance for testing
#             tmp_path: Temporary directory for test files
#         """
#         # Given: Package file metadata and different dataset names
#         package_file = PackageFileMetadata(
#             filename="ds.json",
#             metadatastore_path="metaDataStore/versioned/test_dataset/test_tag/20240201",
#         )

#         # Create files with different dataset names
#         current_path = (
#             tmp_path / "metaDataStore/versioned/test_dataset/test_tag/20240201/ds.json"
#         )
#         previous_path = (
#             tmp_path / "metaDataStore/versioned/test_dataset/test_tag/20240101/ds.json"
#         )

#         current_path.parent.mkdir(parents=True)
#         previous_path.parent.mkdir(parents=True)

#         with open(current_path, "w") as f:
#             json.dump({"name": "new_dataset_name"}, f)
#         with open(previous_path, "w") as f:
#             json.dump({"name": "old_dataset_name"}, f)

#         # When/Then: Comparing should raise DatasetNameChangeException
#         with pytest.raises(DatasetNameChangeException):
#             deploy_package._DeployPackageBuild__compare_dataset_name(
#                 package_file, "20240101", tmp_path
#             )
